<?php
require_once 'config.php';

$page_title = 'الرئيسية';

// Get Categories
$stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
$categories = $stmt->fetchAll();

// Get Latest Products
$stmt = $pdo->query("SELECT p.*, c.name as category_name 
                     FROM products p 
                     LEFT JOIN categories c ON p.category_id = c.id 
                     ORDER BY p.created_at DESC 
                     LIMIT 8");
$products = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="container">
    <section style="text-align: center; padding: 3rem 0;">
        <h1 style="color: var(--primary-blue); font-size: 2.5rem; margin-bottom: 1rem;">
            مرحباً بك في <?= getSetting('site_name', 'Speed Store') ?>
        </h1>
        <p style="color: var(--text-secondary); font-size: 1.2rem;">
            تسوق أفضل المنتجات بأسعار مميزة
        </p>
    </section>

    <!-- Categories Section -->
    <section style="margin-bottom: 3rem;">
        <h2 style="color: var(--dark-blue); margin-bottom: 1.5rem; font-size: 2rem;">
            الأقسام
        </h2>
        <div class="categories-grid">
            <?php foreach ($categories as $category): ?>
                <a href="category.php?slug=<?= $category['slug'] ?>" class="category-card">
                    <h3><?= htmlspecialchars($category['name']) ?></h3>
                </a>
            <?php endforeach; ?>
        </div>
    </section>

    <!-- Latest Products Section -->
    <section>
        <h2 style="color: var(--dark-blue); margin-bottom: 1.5rem; font-size: 2rem;">
            أحدث المنتجات
        </h2>
        <div class="products-grid">
            <?php foreach ($products as $product): ?>
                <div class="product-card">
                    <a href="product.php?slug=<?= $product['slug'] ?>">
                        <?php if ($product['image']): ?>
                            <img src="<?= UPLOAD_URL . $product['image'] ?>" 
                                 alt="<?= htmlspecialchars($product['title']) ?>" 
                                 class="product-image"
                                 onerror="this.src='https://via.placeholder.com/250x200?text=<?= urlencode($product['title']) ?>'">
                        <?php else: ?>
                            <img src="https://via.placeholder.com/250x200?text=<?= urlencode($product['title']) ?>" 
                                 alt="<?= htmlspecialchars($product['title']) ?>" 
                                 class="product-image">
                        <?php endif; ?>
                    </a>
                    <div class="product-info">
                        <h3 class="product-title">
                            <a href="product.php?slug=<?= $product['slug'] ?>" style="text-decoration: none; color: inherit;">
                                <?= htmlspecialchars($product['title']) ?>
                            </a>
                        </h3>
                        <?php if ($product['category_name']): ?>
                            <p style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 0.5rem;">
                                <?= htmlspecialchars($product['category_name']) ?>
                            </p>
                        <?php endif; ?>
                        <p class="product-price"><?= formatPrice($product['price']) ?></p>
                        <form method="post" action="cart.php">
                            <input type="hidden" name="product_id" value="<?= $product['id'] ?>">
                            <input type="hidden" name="action" value="add">
                            <?php if ($product['stock'] > 0): ?>
                                <button type="submit" class="btn btn-primary" style="width: 100%;">
                                    أضف للسلة
                                </button>
                            <?php else: ?>
                                <button type="button" class="btn btn-secondary" style="width: 100%;" disabled>
                                    غير متوفر
                                </button>
                            <?php endif; ?>
                        </form>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </section>
</div>

<?php include 'includes/footer.php'; ?>

