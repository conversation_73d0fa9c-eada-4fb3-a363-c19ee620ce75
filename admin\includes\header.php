<?php
require_once __DIR__ . '/../auth.php';
requireAdmin();

$site_name = getSetting('site_name', 'Speed Store');
$current_page = basename($_SERVER['PHP_SELF'], '.php');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($page_title) ? $page_title . ' - ' : '' ?>لوحة التحكم - <?= $site_name ?></title>
    <link rel="stylesheet" href="<?= SITE_URL ?>/assets/css/style.css">
    <link rel="stylesheet" href="<?= ADMIN_URL ?>/assets/admin.css">
</head>
<body>
    <div class="admin-layout">
        <aside class="admin-sidebar">
            <div class="admin-logo">
                <h2>🛒 <?= $site_name ?></h2>
                <p>لوحة التحكم</p>
            </div>
            <nav class="admin-nav">
                <a href="<?= ADMIN_URL ?>/dashboard.php" class="<?= $current_page === 'dashboard' ? 'active' : '' ?>">
                    📊 لوحة المعلومات
                </a>
                <a href="<?= ADMIN_URL ?>/categories.php" class="<?= $current_page === 'categories' ? 'active' : '' ?>">
                    📁 الأقسام
                </a>
                <a href="<?= ADMIN_URL ?>/products.php" class="<?= $current_page === 'products' ? 'active' : '' ?>">
                    📦 المنتجات
                </a>
                <a href="<?= ADMIN_URL ?>/orders.php" class="<?= $current_page === 'orders' ? 'active' : '' ?>">
                    🛍️ الطلبات
                </a>
                <a href="<?= ADMIN_URL ?>/settings.php" class="<?= $current_page === 'settings' ? 'active' : '' ?>">
                    ⚙️ الإعدادات
                </a>
                <a href="<?= SITE_URL ?>" target="_blank">
                    🌐 زيارة الموقع
                </a>
                <a href="<?= ADMIN_URL ?>/logout.php" style="color: var(--error);">
                    🚪 تسجيل الخروج
                </a>
            </nav>
            <div class="admin-user">
                <p>مرحباً، <strong><?= $_SESSION['admin_username'] ?></strong></p>
            </div>
        </aside>
        <main class="admin-content">

