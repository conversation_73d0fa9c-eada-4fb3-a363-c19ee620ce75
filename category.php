<?php
require_once 'config.php';

// Get category slug from URL
$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    redirect(SITE_URL);
}

// Get category
$stmt = $pdo->prepare("SELECT * FROM categories WHERE slug = ?");
$stmt->execute([$slug]);
$category = $stmt->fetch();

if (!$category) {
    redirect(SITE_URL);
}

$page_title = $category['name'];

// Get products in this category
$stmt = $pdo->prepare("SELECT * FROM products WHERE category_id = ? ORDER BY created_at DESC");
$stmt->execute([$category['id']]);
$products = $stmt->fetchAll();

include 'includes/header.php';
?>

<div class="container">
    <div style="margin-bottom: 2rem;">
        <a href="<?= SITE_URL ?>" style="color: var(--primary-blue); text-decoration: none;">
            ← العودة للرئيسية
        </a>
    </div>

    <h1 style="color: var(--dark-blue); margin-bottom: 2rem; font-size: 2.5rem;">
        <?= htmlspecialchars($category['name']) ?>
    </h1>

    <?php if (empty($products)): ?>
        <div class="alert alert-info">
            لا توجد منتجات في هذا القسم حالياً
        </div>
    <?php else: ?>
        <div class="products-grid">
            <?php foreach ($products as $product): ?>
                <div class="product-card">
                    <a href="product.php?slug=<?= $product['slug'] ?>">
                        <?php if ($product['image']): ?>
                            <img src="<?= UPLOAD_URL . $product['image'] ?>" 
                                 alt="<?= htmlspecialchars($product['title']) ?>" 
                                 class="product-image"
                                 onerror="this.src='https://via.placeholder.com/250x200?text=<?= urlencode($product['title']) ?>'">
                        <?php else: ?>
                            <img src="https://via.placeholder.com/250x200?text=<?= urlencode($product['title']) ?>" 
                                 alt="<?= htmlspecialchars($product['title']) ?>" 
                                 class="product-image">
                        <?php endif; ?>
                    </a>
                    <div class="product-info">
                        <h3 class="product-title">
                            <a href="product.php?slug=<?= $product['slug'] ?>" style="text-decoration: none; color: inherit;">
                                <?= htmlspecialchars($product['title']) ?>
                            </a>
                        </h3>
                        <p class="product-price"><?= formatPrice($product['price']) ?></p>
                        <form method="post" action="cart.php">
                            <input type="hidden" name="product_id" value="<?= $product['id'] ?>">
                            <input type="hidden" name="action" value="add">
                            <?php if ($product['stock'] > 0): ?>
                                <button type="submit" class="btn btn-primary" style="width: 100%;">
                                    أضف للسلة
                                </button>
                            <?php else: ?>
                                <button type="button" class="btn btn-secondary" style="width: 100%;" disabled>
                                    غير متوفر
                                </button>
                            <?php endif; ?>
                        </form>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>

