/* Speed Store - Main Stylesheet */

:root {
    --primary-blue: #0869d0;
    --primary-white: #fcfdfc;
    --dark-blue: #064a91;
    --light-gray: #f4f6f8;
    --hover-blue: #3c8deb;
    --badge-highlight: #b3d4f9;
    --text-main: #212529;
    --text-secondary: #6c757d;
    --text-on-dark: #ffffff;
    --success: #28a745;
    --warning: #ffc107;
    --error: #dc3545;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--primary-white);
    color: var(--text-main);
    direction: rtl;
    text-align: right;
}

/* Header */
.site-header {
    background-color: var(--dark-blue);
    color: var(--text-on-dark);
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.site-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.site-logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-on-dark);
    text-decoration: none;
}

.site-nav ul {
    list-style: none;
    display: flex;
    gap: 2rem;
    margin: 0;
}

.site-nav a {
    color: var(--text-on-dark);
    text-decoration: none;
    transition: color 0.3s;
}

.site-nav a:hover {
    color: var(--badge-highlight);
}

.cart-icon {
    position: relative;
}

.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--error);
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.75rem;
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.5rem 1.5rem;
    border-radius: 5px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--primary-blue);
    color: white;
}

.btn-primary:hover {
    background-color: var(--hover-blue);
}

.btn-secondary {
    background-color: white;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
    background-color: var(--light-gray);
}

.btn-success {
    background-color: var(--success);
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-danger {
    background-color: var(--error);
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Product Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.product-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.product-info {
    padding: 1rem;
}

.product-title {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: var(--text-main);
}

.product-price {
    font-size: 1.3rem;
    color: var(--primary-blue);
    font-weight: bold;
    margin-bottom: 1rem;
}

.product-description {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

/* Categories */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.category-card {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    text-decoration: none;
    color: var(--text-main);
    transition: all 0.3s;
}

.category-card:hover {
    background: var(--primary-blue);
    color: white;
    transform: scale(1.05);
}

/* Cart Table */
.cart-table {
    width: 100%;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.cart-table th,
.cart-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--light-gray);
}

.cart-table th {
    background-color: var(--dark-blue);
    color: white;
}

.cart-total {
    background: var(--light-gray);
    padding: 1.5rem;
    border-radius: 8px;
    margin-top: 1rem;
    text-align: left;
}

.cart-total h3 {
    font-size: 1.5rem;
    color: var(--primary-blue);
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-blue);
}

/* Footer */
.site-footer {
    background-color: var(--dark-blue);
    color: var(--text-on-dark);
    padding: 2rem 0;
    margin-top: 3rem;
    text-align: center;
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Invoice */
.invoice-container {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    max-width: 800px;
    margin: 2rem auto;
}

.invoice-header {
    text-align: center;
    border-bottom: 2px solid var(--primary-blue);
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.invoice-details {
    margin-bottom: 2rem;
}

.invoice-table {
    width: 100%;
    margin-bottom: 2rem;
}

.invoice-table th,
.invoice-table td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--light-gray);
}

.invoice-total {
    text-align: left;
    font-size: 1.5rem;
    color: var(--primary-blue);
    font-weight: bold;
}

/* Responsive */
@media (max-width: 768px) {
    .site-header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .site-nav ul {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
}

