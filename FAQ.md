# ❓ الأسئلة الشائعة - Speed Store

## 📋 جدول المحتويات

1. [أسئلة عامة](#أسئلة-عامة)
2. [التثبيت](#التثبيت)
3. [الاستخدام](#الاستخدام)
4. [المشاكل الشائعة](#المشاكل-الشائعة)
5. [الأمان](#الأمان)
6. [التخصيص](#التخصيص)
7. [الأداء](#الأداء)

---

## 🌟 أسئلة عامة

### س: ما هو Speed Store؟
**ج:** Speed Store هو متجر إلكتروني بسيط مكتوب بـ PHP و MySQL، مصمم خصيصاً للمتاجر الصغيرة والمتوسطة التي تبيع عبر واتساب.

### س: هل Speed Store مجاني؟
**ج:** نعم، Speed Store مفتوح المصدر ومجاني تماماً للاستخدام الشخصي والتجاري.

### س: ما هي الترخيص؟
**ج:** مرخص تحت MIT License، يمكنك استخدامه وتعديله بحرية.

### س: هل يدعم اللغة العربية؟
**ج:** نعم، الواجهة بالكامل باللغة العربية مع دعم RTL.

### س: هل يمكنني استخدامه لمتجري؟
**ج:** نعم، يمكنك استخدامه لأي نوع من المتاجر.

---

## 🔧 التثبيت

### س: ما هي متطلبات التشغيل؟
**ج:** 
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache مع mod_rewrite
- مكتبة GD لمعالجة الصور

### س: كيف أثبت Speed Store؟
**ج:** اتبع الخطوات في ملف `INSTALLATION.md`:
1. رفع الملفات
2. إنشاء قاعدة البيانات
3. تعديل config.php
4. ضبط الصلاحيات

### س: هل يعمل على الاستضافة المشتركة؟
**ج:** نعم، يعمل على معظم خدمات الاستضافة المشتركة التي تدعم PHP و MySQL.

### س: هل يعمل على localhost؟
**ج:** نعم، يمكنك تشغيله على XAMPP أو WAMP أو MAMP.

### س: كيف أستورد قاعدة البيانات؟
**ج:** 
```bash
# عبر phpMyAdmin
1. افتح phpMyAdmin
2. اختر Import
3. اختر ملف database.sql
4. اضغط Go

# عبر سطر الأوامر
mysql -u username -p < database.sql
```

---

## 💼 الاستخدام

### س: كيف أضيف منتج؟
**ج:**
1. سجل دخول للوحة التحكم
2. اذهب إلى **المنتجات**
3. املأ النموذج
4. اضغط **إضافة**

### س: كيف أغير رقم واتساب؟
**ج:**
1. اذهب إلى **الإعدادات**
2. أدخل الرقم بصيغة: `9647700000000`
3. احفظ

### س: كيف أتابع الطلبات؟
**ج:**
1. اذهب إلى **الطلبات**
2. اضغط **عرض** على أي طلب
3. غيّر الحالة من القائمة

### س: كيف أحذف منتج؟
**ج:**
1. اذهب إلى **المنتجات**
2. اضغط **حذف** بجانب المنتج
3. أكّد الحذف

### س: كيف أعدل سعر منتج؟
**ج:**
1. اذهب إلى **المنتجات**
2. اضغط **تعديل**
3. غيّر السعر
4. احفظ

---

## 🐛 المشاكل الشائعة

### س: خطأ "Could not connect to database"
**ج:** تحقق من:
- بيانات الاتصال في `config.php`
- أن MySQL يعمل
- أن قاعدة البيانات موجودة
- صلاحيات المستخدم

### س: الصور لا تظهر
**ج:** تحقق من:
- صلاحيات مجلد `uploads/` (755)
- صحة `SITE_URL` في `config.php`
- أن الصور تم رفعها بنجاح
- مسار الصور صحيح

### س: خطأ 404 في الصفحات
**ج:** تحقق من:
- وجود ملف `.htaccess`
- تفعيل `mod_rewrite` في Apache
- صحة `SITE_URL` في `config.php`

### س: لا يمكن رفع الصور
**ج:** تحقق من:
- صلاحيات مجلد `uploads/`
- إعدادات PHP:
  ```php
  upload_max_filesize = 10M
  post_max_size = 10M
  ```
- نوع الملف (jpg, png, gif)

### س: لا يمكن تسجيل الدخول
**ج:** 
- استخدم: admin / admin123
- تأكد من استيراد `database.sql`
- امسح الكوكيز والكاش

### س: رسالة "Session expired"
**ج:**
- سجل دخول مرة أخرى
- تحقق من إعدادات Session في PHP
- تأكد من أن الكوكيز مفعلة

---

## 🔒 الأمان

### س: كيف أغير كلمة المرور؟
**ج:**
```php
// 1. أنشئ hash جديد
<?php
echo password_hash('كلمة_المرور_الجديدة', PASSWORD_DEFAULT);
?>

// 2. حدّث قاعدة البيانات
UPDATE admin_users 
SET password = 'الناتج_من_الكود' 
WHERE username = 'admin';
```

### س: هل Speed Store آمن؟
**ج:** نعم، يستخدم:
- تشفير كلمات المرور (bcrypt)
- Prepared Statements ضد SQL Injection
- التحقق من الملفات المرفوعة
- حماية صفحات الإدارة

### س: كيف أحمي ملف config.php؟
**ج:** ملف `.htaccess` يحميه تلقائياً:
```apache
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>
```

### س: هل يجب استخدام HTTPS؟
**ج:** نعم، يُنصح بشدة باستخدام HTTPS لحماية البيانات.

### س: كيف أفعّل HTTPS؟
**ج:**
1. احصل على شهادة SSL
2. فعّل HTTPS في `.htaccess`:
```apache
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

---

## 🎨 التخصيص

### س: كيف أغير الألوان؟
**ج:** عدّل `assets/css/style.css`:
```css
:root {
    --primary-blue: #0869d0;  /* غيّر هذا */
    --dark-blue: #064a91;     /* وهذا */
}
```

### س: كيف أضيف شعار؟
**ج:**
1. ارفع الشعار إلى `uploads/logo.png`
2. عدّل `includes/header.php`:
```php
<img src="<?= SITE_URL ?>/uploads/logo.png" alt="Logo">
```

### س: كيف أغير اسم المتجر؟
**ج:**
1. اذهب إلى **الإعدادات**
2. غيّر **اسم المتجر**
3. احفظ

### س: كيف أضيف صفحة جديدة؟
**ج:**
1. أنشئ ملف PHP جديد
2. استخدم `includes/header.php` و `includes/footer.php`
3. أضف رابط في القائمة

### س: كيف أغير التصميم؟
**ج:** عدّل ملف `assets/css/style.css` حسب رغبتك.

---

## ⚡ الأداء

### س: كيف أحسّن الأداء؟
**ج:**
- استخدم Cache
- ضغط الصور
- تفعيل Gzip
- استخدام CDN

### س: الموقع بطيء، ماذا أفعل؟
**ج:** تحقق من:
- سرعة السيرفر
- حجم الصور (ضغطها)
- عدد الاستعلامات
- استخدام الفهرسة في قاعدة البيانات

### س: كيف أضغط الصور؟
**ج:** استخدم أدوات مثل:
- TinyPNG
- ImageOptim
- Photoshop (Save for Web)

### س: كم عدد المنتجات المدعومة؟
**ج:** يدعم آلاف المنتجات، لكن يُنصح بالتقسيم إلى أقسام.

---

## 📱 واتساب

### س: كيف يعمل نظام واتساب؟
**ج:**
1. العميل يتمم الطلب
2. يحصل على فاتورة
3. يضغط "إرسال عبر واتساب"
4. يفتح واتساب مع رسالة جاهزة
5. يرسلها للمتجر

### س: ما تنسيق رقم واتساب؟
**ج:**
```
الصيغة: [رمز الدولة][رقم الهاتف]
العراق: 9647700000000
السعودية: 966500000000
مصر: 201000000000
```
(بدون + أو 00)

### س: واتساب لا يفتح؟
**ج:** تحقق من:
- صحة رقم واتساب في الإعدادات
- أن واتساب مثبت على الجهاز
- أن الرابط صحيح

### س: هل يمكن إرسال الطلب تلقائياً؟
**ج:** لا، العميل يجب أن يضغط "إرسال" في واتساب.

---

## 💳 الدفع

### س: هل يدعم الدفع الإلكتروني؟
**ج:** حالياً لا، يدعم الدفع عند الاستلام فقط.

### س: كيف أضيف بوابة دفع؟
**ج:** تحتاج تطوير إضافي لدمج بوابات الدفع مثل PayPal أو Stripe.

### س: هل يدعم ZainCash؟
**ج:** حالياً لا، لكن يمكن إضافته في المستقبل.

---

## 🌍 اللغات

### س: هل يدعم الإنجليزية؟
**ج:** حالياً العربية فقط، لكن يمكن إضافة لغات أخرى.

### س: كيف أضيف لغة جديدة؟
**ج:** تحتاج:
1. إنشاء ملفات ترجمة
2. تعديل الكود لدعم متعدد اللغات
3. إضافة مبدل اللغة

---

## 📊 التقارير

### س: هل يوجد تقارير مبيعات؟
**ج:** حالياً لا، لكن يمكنك:
- عرض الطلبات في لوحة التحكم
- تصدير البيانات من قاعدة البيانات

### س: كيف أصدّر الطلبات؟
**ج:**
```sql
SELECT * FROM orders 
WHERE created_at >= '2025-01-01'
INTO OUTFILE '/tmp/orders.csv';
```

---

## 🔄 النسخ الاحتياطي

### س: كيف أعمل نسخة احتياطية؟
**ج:**
```bash
# قاعدة البيانات
mysqldump -u username -p speed_store > backup.sql

# الملفات
cp -r uploads/ backup/uploads/
cp config.php backup/
```

### س: كم مرة يجب عمل نسخة احتياطية؟
**ج:** يُنصح بعمل نسخة احتياطية:
- يومياً للمتاجر النشطة
- أسبوعياً للمتاجر الصغيرة

---

## 🆘 الدعم

### س: أين أجد المساعدة؟
**ج:**
- اقرأ ملف `README.md`
- راجع `INSTALLATION.md`
- اقرأ `USAGE_GUIDE.md`
- افتح Issue على GitHub

### س: كيف أبلغ عن خطأ؟
**ج:** افتح Issue على GitHub مع:
- وصف الخطأ
- خطوات إعادة الإنتاج
- لقطات شاشة
- معلومات البيئة

### س: كيف أقترح ميزة؟
**ج:** افتح Issue على GitHub مع:
- وصف الميزة
- الفائدة المتوقعة
- أمثلة على الاستخدام

---

## 🎓 التعلم

### س: هل أحتاج خبرة برمجية؟
**ج:** 
- للاستخدام: لا
- للتخصيص البسيط: معرفة أساسية بـ HTML/CSS
- للتطوير: معرفة بـ PHP و MySQL

### س: أين أتعلم PHP؟
**ج:**
- [PHP.net](https://www.php.net)
- [W3Schools](https://www.w3schools.com/php/)
- [PHP The Right Way](https://phptherightway.com)

---

## 📞 التواصل

### س: كيف أتواصل مع الفريق؟
**ج:**
- GitHub Issues
- Pull Requests
- Discussions

---

**💡 لم تجد إجابة؟ افتح Issue على GitHub!**

