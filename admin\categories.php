<?php
$page_title = 'إدارة الأقسام';
include 'includes/header.php';

$success = '';
$error = '';

// Handle Add Category
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add') {
    $name = trim($_POST['name'] ?? '');
    $slug = trim($_POST['slug'] ?? '');
    
    if (empty($name)) {
        $error = 'الرجاء إدخال اسم القسم';
    } else {
        if (empty($slug)) {
            $slug = generateSlug($name);
        }
        
        try {
            $stmt = $pdo->prepare("INSERT INTO categories (name, slug) VALUES (?, ?)");
            $stmt->execute([$name, $slug]);
            $success = 'تم إضافة القسم بنجاح';
        } catch (PDOException $e) {
            $error = 'خطأ: القسم موجود مسبقاً أو الرابط مكرر';
        }
    }
}

// Handle Edit Category
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'edit') {
    $id = intval($_POST['id'] ?? 0);
    $name = trim($_POST['name'] ?? '');
    $slug = trim($_POST['slug'] ?? '');
    
    if ($id > 0 && !empty($name)) {
        if (empty($slug)) {
            $slug = generateSlug($name);
        }
        
        try {
            $stmt = $pdo->prepare("UPDATE categories SET name = ?, slug = ? WHERE id = ?");
            $stmt->execute([$name, $slug, $id]);
            $success = 'تم تحديث القسم بنجاح';
        } catch (PDOException $e) {
            $error = 'خطأ: الرابط مكرر';
        }
    }
}

// Handle Delete Category
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);
    
    try {
        $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
        $stmt->execute([$id]);
        $success = 'تم حذف القسم بنجاح';
    } catch (PDOException $e) {
        $error = 'خطأ: لا يمكن حذف القسم';
    }
}

// Get all categories
$stmt = $pdo->query("SELECT c.*, COUNT(p.id) as products_count 
                     FROM categories c 
                     LEFT JOIN products p ON c.id = p.category_id 
                     GROUP BY c.id 
                     ORDER BY c.name");
$categories = $stmt->fetchAll();

// Get category for editing
$edit_category = null;
if (isset($_GET['edit'])) {
    $id = intval($_GET['edit']);
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
    $stmt->execute([$id]);
    $edit_category = $stmt->fetch();
}
?>

<div class="admin-header">
    <h1>إدارة الأقسام</h1>
</div>

<?php if ($success): ?>
    <div class="alert alert-success"><?= $success ?></div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error"><?= $error ?></div>
<?php endif; ?>

<div style="display: grid; grid-template-columns: 1fr 2fr; gap: 1.5rem;">
    <!-- Add/Edit Form -->
    <div class="admin-card">
        <h2><?= $edit_category ? 'تعديل القسم' : 'إضافة قسم جديد' ?></h2>
        <form method="post">
            <input type="hidden" name="action" value="<?= $edit_category ? 'edit' : 'add' ?>">
            <?php if ($edit_category): ?>
                <input type="hidden" name="id" value="<?= $edit_category['id'] ?>">
            <?php endif; ?>
            
            <div class="form-group">
                <label for="name">اسم القسم *</label>
                <input type="text" 
                       name="name" 
                       id="name" 
                       class="form-control" 
                       value="<?= htmlspecialchars($edit_category['name'] ?? '') ?>"
                       required>
            </div>
            
            <div class="form-group">
                <label for="slug">الرابط (Slug)</label>
                <input type="text" 
                       name="slug" 
                       id="slug" 
                       class="form-control" 
                       value="<?= htmlspecialchars($edit_category['slug'] ?? '') ?>"
                       placeholder="يتم إنشاؤه تلقائياً">
                <small style="color: var(--text-secondary);">اتركه فارغاً للإنشاء التلقائي</small>
            </div>
            
            <button type="submit" class="btn btn-primary" style="width: 100%;">
                <?= $edit_category ? 'تحديث' : 'إضافة' ?>
            </button>
            
            <?php if ($edit_category): ?>
                <a href="categories.php" class="btn btn-secondary" style="width: 100%; margin-top: 0.5rem; text-align: center; display: block; text-decoration: none;">
                    إلغاء
                </a>
            <?php endif; ?>
        </form>
    </div>

    <!-- Categories List -->
    <div class="admin-card">
        <h2>قائمة الأقسام</h2>
        <?php if (empty($categories)): ?>
            <p style="color: var(--text-secondary);">لا توجد أقسام</p>
        <?php else: ?>
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>الاسم</th>
                        <th>الرابط</th>
                        <th>عدد المنتجات</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($categories as $category): ?>
                        <tr>
                            <td><?= $category['id'] ?></td>
                            <td><?= htmlspecialchars($category['name']) ?></td>
                            <td><code><?= htmlspecialchars($category['slug']) ?></code></td>
                            <td><?= $category['products_count'] ?></td>
                            <td>
                                <div class="btn-group">
                                    <a href="?edit=<?= $category['id'] ?>" class="btn btn-primary btn-sm">تعديل</a>
                                    <a href="?delete=<?= $category['id'] ?>" 
                                       class="btn btn-danger btn-sm"
                                       onclick="return confirm('هل أنت متأكد من حذف هذا القسم؟')">حذف</a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<style>
    @media (max-width: 768px) {
        .admin-content > div:last-child {
            grid-template-columns: 1fr !important;
        }
    }
</style>

<?php include 'includes/footer.php'; ?>

