CREATE DATABASE IF NOT EXISTS speed_store CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE speed_store;

CREATE TABLE categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(120) NOT NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE products (
  id INT AUTO_INCREMENT PRIMARY KEY,
  category_id INT,
  title VARCHAR(200) NOT NULL,
  slug VARCHAR(220) NOT NULL UNIQUE,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  stock INT DEFAULT 0,
  image VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

CREATE TABLE orders (
  id INT AUTO_INCREMENT PRIMARY KEY,
  customer_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20) NOT NULL,
  address TEXT,
  notes TEXT,
  total DECIMAL(12,2) NOT NULL,
  status VARCHAR(50) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE order_items (
  id INT AUTO_INCREMENT PRIMARY KEY,
  order_id INT,
  product_id INT,
  qty INT NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL
);

CREATE TABLE admin_users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(100) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE settings (
  `key` VARCHAR(100) PRIMARY KEY,
  `value` TEXT
);

-- Insert Default Admin User (username: admin, password: admin123)
INSERT INTO admin_users (username, password) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');

-- Insert Default Settings
INSERT INTO settings (`key`, `value`) VALUES
('site_name', 'Speed Store'),
('whatsapp_number', '9647700000000'),
('site_address', 'بغداد، العراق'),
('site_email', '<EMAIL>'),
('currency', 'IQD');

-- Insert Sample Categories
INSERT INTO categories (name, slug) VALUES
('إلكترونيات', 'electronics'),
('ملابس', 'clothing'),
('أحذية', 'shoes'),
('إكسسوارات', 'accessories');

-- Insert Sample Products
INSERT INTO products (category_id, title, slug, description, price, stock, image) VALUES
(1, 'هاتف ذكي سامسونج', 'samsung-phone', 'هاتف ذكي بمواصفات عالية وكاميرا ممتازة', 450000.00, 10, 'phone.jpg'),
(1, 'لابتوب ديل', 'dell-laptop', 'لابتوب قوي للعمل والألعاب', 850000.00, 5, 'laptop.jpg'),
(2, 'قميص رجالي', 'mens-shirt', 'قميص قطني عالي الجودة', 25000.00, 20, 'shirt.jpg'),
(3, 'حذاء رياضي', 'sports-shoes', 'حذاء رياضي مريح للجري', 75000.00, 15, 'shoes.jpg');

