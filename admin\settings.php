<?php
$page_title = 'الإعدادات';
include 'includes/header.php';

$success = '';
$error = '';

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $site_name = trim($_POST['site_name'] ?? '');
    $whatsapp_number = trim($_POST['whatsapp_number'] ?? '');
    $site_address = trim($_POST['site_address'] ?? '');
    $site_email = trim($_POST['site_email'] ?? '');
    $currency = trim($_POST['currency'] ?? 'IQD');
    
    if (empty($site_name)) {
        $error = 'الرجاء إدخال اسم المتجر';
    } else {
        setSetting('site_name', $site_name);
        setSetting('whatsapp_number', $whatsapp_number);
        setSetting('site_address', $site_address);
        setSetting('site_email', $site_email);
        setSetting('currency', $currency);
        
        $success = 'تم حفظ الإعدادات بنجاح';
    }
}

// Get current settings
$site_name = getSetting('site_name', 'Speed Store');
$whatsapp_number = getSetting('whatsapp_number', '9647700000000');
$site_address = getSetting('site_address', '');
$site_email = getSetting('site_email', '');
$currency = getSetting('currency', 'IQD');
?>

<div class="admin-header">
    <h1>إعدادات المتجر</h1>
</div>

<?php if ($success): ?>
    <div class="alert alert-success"><?= $success ?></div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error"><?= $error ?></div>
<?php endif; ?>

<div class="admin-card">
    <h2>الإعدادات العامة</h2>
    <form method="post">
        <div class="form-row">
            <div class="form-group">
                <label for="site_name">اسم المتجر *</label>
                <input type="text" 
                       name="site_name" 
                       id="site_name" 
                       class="form-control" 
                       value="<?= htmlspecialchars($site_name) ?>"
                       required>
            </div>
            
            <div class="form-group">
                <label for="currency">العملة</label>
                <input type="text" 
                       name="currency" 
                       id="currency" 
                       class="form-control" 
                       value="<?= htmlspecialchars($currency) ?>"
                       placeholder="IQD">
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="whatsapp_number">رقم واتساب المتجر</label>
                <input type="text" 
                       name="whatsapp_number" 
                       id="whatsapp_number" 
                       class="form-control" 
                       value="<?= htmlspecialchars($whatsapp_number) ?>"
                       placeholder="9647700000000">
                <small style="color: var(--text-secondary);">
                    مثال: 9647700000000 (بدون + أو 00)
                </small>
            </div>
            
            <div class="form-group">
                <label for="site_email">البريد الإلكتروني</label>
                <input type="email" 
                       name="site_email" 
                       id="site_email" 
                       class="form-control" 
                       value="<?= htmlspecialchars($site_email) ?>"
                       placeholder="<EMAIL>">
            </div>
        </div>
        
        <div class="form-group">
            <label for="site_address">عنوان المتجر</label>
            <textarea name="site_address" 
                      id="site_address" 
                      class="form-control" 
                      rows="3"><?= htmlspecialchars($site_address) ?></textarea>
        </div>
        
        <button type="submit" class="btn btn-primary" style="padding: 0.75rem 2rem;">
            حفظ الإعدادات
        </button>
    </form>
</div>

<div class="admin-card">
    <h2>معلومات النظام</h2>
    <table style="width: 100%;">
        <tr>
            <td style="padding: 0.5rem; border-bottom: 1px solid var(--light-gray);"><strong>إصدار PHP:</strong></td>
            <td style="padding: 0.5rem; border-bottom: 1px solid var(--light-gray);"><?= phpversion() ?></td>
        </tr>
        <tr>
            <td style="padding: 0.5rem; border-bottom: 1px solid var(--light-gray);"><strong>قاعدة البيانات:</strong></td>
            <td style="padding: 0.5rem; border-bottom: 1px solid var(--light-gray);">MySQL</td>
        </tr>
        <tr>
            <td style="padding: 0.5rem; border-bottom: 1px solid var(--light-gray);"><strong>مجلد الرفع:</strong></td>
            <td style="padding: 0.5rem; border-bottom: 1px solid var(--light-gray);">
                <?= is_writable(UPLOAD_DIR) ? '<span style="color: var(--success);">✓ قابل للكتابة</span>' : '<span style="color: var(--error);">✗ غير قابل للكتابة</span>' ?>
            </td>
        </tr>
        <tr>
            <td style="padding: 0.5rem;"><strong>رابط الموقع:</strong></td>
            <td style="padding: 0.5rem;">
                <a href="<?= SITE_URL ?>" target="_blank" style="color: var(--primary-blue);"><?= SITE_URL ?></a>
            </td>
        </tr>
    </table>
</div>

<div class="admin-card">
    <h2>تغيير كلمة المرور</h2>
    <p style="color: var(--text-secondary); margin-bottom: 1rem;">
        لتغيير كلمة المرور، يرجى تحديث جدول admin_users في قاعدة البيانات مباشرة.
    </p>
    <div style="background: var(--light-gray); padding: 1rem; border-radius: 5px; font-family: monospace; font-size: 0.9rem;">
        <strong>مثال SQL:</strong><br>
        UPDATE admin_users SET password = '$2y$10$...' WHERE username = 'admin';
    </div>
</div>

<?php include 'includes/footer.php'; ?>

