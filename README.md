# 🛒 Speed Store - متجر إلكتروني بسيط

متجر إلكتروني متكامل مكتوب بـ PHP + MySQL مع واجهة عربية كاملة ولوحة تحكم شاملة.

## ✨ المميزات

### واجهة الزبون
- 🏠 صفحة رئيسية تعرض الأقسام وأحدث المنتجات
- 📁 تصفح المنتجات حسب الأقسام
- 🔍 صفحة تفاصيل المنتج مع الصور والوصف
- 🛒 سلة مشتريات سهلة الاستخدام
- 📝 نموذج إتمام الطلب
- 🧾 فاتورة رقمية بعد إتمام الطلب
- 📱 إرسال الطلب مباشرة عبر واتساب

### لوحة التحكم
- 📊 لوحة معلومات شاملة
- 📁 إدارة الأقسام (إضافة، تعديل، حذف)
- 📦 إدارة المنتجات مع رفع الصور
- 🛍️ إدارة الطلبات ومتابعة حالتها
- ⚙️ إعدادات المتجر (اسم المتجر، رقم واتساب، العنوان)
- 🔐 نظام تسجيل دخول آمن

## 🎨 الألوان المعتمدة

```css
Primary Blue: #0869d0
Primary White: #fcfdfc
Dark Blue: #064a91
Light Gray: #f4f6f8
Hover Blue: #3c8deb
Badge Highlight: #b3d4f9
Success: #28a745
Warning: #ffc107
Error: #dc3545
```

## 📋 المتطلبات

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx مع mod_rewrite
- مكتبة GD لمعالجة الصور

## 🚀 التثبيت

### 1. رفع الملفات
قم برفع جميع الملفات إلى مجلد الموقع على السيرفر.

### 2. إنشاء قاعدة البيانات
1. افتح phpMyAdmin أو أي أداة لإدارة MySQL
2. قم باستيراد ملف `database.sql`
3. سيتم إنشاء قاعدة البيانات `speed_store` تلقائياً

### 3. تعديل إعدادات الاتصال
افتح ملف `config.php` وعدّل البيانات التالية:

```php
define('DB_HOST', 'localhost');      // عنوان السيرفر
define('DB_USER', 'root');           // اسم المستخدم
define('DB_PASS', '');               // كلمة المرور
define('DB_NAME', 'speed_store');    // اسم قاعدة البيانات
define('SITE_URL', 'http://localhost/speed_store'); // رابط الموقع
```

### 4. إنشاء مجلد الرفع
تأكد من أن مجلد `uploads/` موجود وله صلاحيات الكتابة (755 أو 777).

```bash
mkdir uploads
chmod 755 uploads
```

### 5. الوصول للموقع
- **الموقع الرئيسي:** `http://yoursite.com`
- **لوحة التحكم:** `http://yoursite.com/admin`

## 🔐 بيانات الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: admin123
```

**⚠️ مهم:** قم بتغيير كلمة المرور فوراً بعد التثبيت!

## 📁 هيكل المشروع

```
speed_store/
├── admin/                  # لوحة التحكم
│   ├── assets/            # ملفات CSS الخاصة بالإدارة
│   ├── includes/          # Header & Footer
│   ├── auth.php           # نظام المصادقة
│   ├── dashboard.php      # لوحة المعلومات
│   ├── categories.php     # إدارة الأقسام
│   ├── products.php       # إدارة المنتجات
│   ├── orders.php         # إدارة الطلبات
│   ├── settings.php       # الإعدادات
│   ├── login.php          # تسجيل الدخول
│   └── logout.php         # تسجيل الخروج
├── assets/                # ملفات CSS & JS
│   └── css/
│       └── style.css      # التصميم الرئيسي
├── includes/              # Header & Footer للموقع
├── uploads/               # مجلد الصور المرفوعة
├── config.php             # إعدادات الاتصال
├── database.sql           # قاعدة البيانات
├── index.php              # الصفحة الرئيسية
├── category.php           # صفحة القسم
├── product.php            # صفحة المنتج
├── cart.php               # السلة
├── checkout.php           # إتمام الطلب
├── invoice.php            # الفاتورة
└── README.md              # هذا الملف
```

## 🔧 الإعدادات

### تغيير رقم واتساب
1. سجل دخول للوحة التحكم
2. اذهب إلى **الإعدادات**
3. أدخل رقم واتساب بالصيغة: `9647700000000` (بدون + أو 00)

### إضافة منتجات
1. اذهب إلى **المنتجات** في لوحة التحكم
2. املأ النموذج (الاسم، السعر، المخزون، الصورة)
3. اضغط **إضافة**

### إدارة الطلبات
1. اذهب إلى **الطلبات**
2. اضغط **عرض** لرؤية تفاصيل الطلب
3. غيّر الحالة من القائمة المنسدلة

## 📱 ميزة واتساب

عند إتمام الطلب، يحصل الزبون على:
- فاتورة رقمية تعرض تفاصيل الطلب
- زر لإرسال الطلب مباشرة إلى واتساب المتجر
- الرسالة تحتوي على: رقم الطلب، اسم العميل، المنتجات، المبلغ الإجمالي

## 🎨 التخصيص

### تغيير الألوان
عدّل ملف `assets/css/style.css` في قسم `:root`:

```css
:root {
    --primary-blue: #0869d0;
    --dark-blue: #064a91;
    /* ... */
}
```

### إضافة شعار
1. ارفع صورة الشعار إلى مجلد `uploads/`
2. عدّل ملف `includes/header.php`

## 🔒 الأمان

- كلمات المرور مشفرة باستخدام `password_hash()`
- حماية من SQL Injection باستخدام Prepared Statements
- التحقق من صلاحيات المدير في كل صفحة
- التحقق من نوع الملفات المرفوعة

## 🐛 استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
- تأكد من صحة بيانات الاتصال في `config.php`
- تأكد من أن MySQL يعمل

### الصور لا تظهر
- تأكد من أن مجلد `uploads/` له صلاحيات الكتابة
- تأكد من صحة مسار `UPLOAD_URL` في `config.php`

### لا يمكن تسجيل الدخول
- تأكد من استيراد ملف `database.sql` بشكل صحيح
- استخدم البيانات الافتراضية: admin / admin123

## 📞 الدعم

للمساعدة أو الاستفسارات، يمكنك:
- فتح Issue على GitHub
- التواصل عبر البريد الإلكتروني

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## 🙏 شكر خاص

تم تطوير هذا المشروع باستخدام:
- PHP
- MySQL
- CSS3
- JavaScript

---

**صُنع بـ ❤️ في العراق**

