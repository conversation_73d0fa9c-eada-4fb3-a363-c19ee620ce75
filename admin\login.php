<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/auth.php';

// Redirect if already logged in
if (isAdminLoggedIn()) {
    redirect(ADMIN_URL . '/dashboard.php');
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    
    if (empty($username) || empty($password)) {
        $error = 'الرجاء إدخال اسم المستخدم وكلمة المرور';
    } else {
        if (adminLogin($username, $password)) {
            redirect(ADMIN_URL . '/dashboard.php');
        } else {
            $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة التحكم</title>
    <link rel="stylesheet" href="<?= SITE_URL ?>/assets/css/style.css">
    <link rel="stylesheet" href="<?= ADMIN_URL ?>/assets/admin.css">
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <h1>🛒 لوحة التحكم</h1>
            <h2 style="text-align: center; color: var(--text-secondary); margin-bottom: 2rem; font-size: 1.2rem;">
                <?= getSetting('site_name', 'Speed Store') ?>
            </h2>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-error">
                    <?= $error ?>
                </div>
            <?php endif; ?>
            
            <form method="post">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <input type="text" 
                           name="username" 
                           id="username" 
                           class="form-control" 
                           value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                           required 
                           autofocus>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" 
                           name="password" 
                           id="password" 
                           class="form-control" 
                           required>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    تسجيل الدخول
                </button>
            </form>
            
            <div style="text-align: center; margin-top: 2rem; padding-top: 1rem; border-top: 1px solid var(--light-gray);">
                <a href="<?= SITE_URL ?>" style="color: var(--primary-blue); text-decoration: none;">
                    ← العودة للموقع
                </a>
            </div>
            
            <div style="margin-top: 2rem; padding: 1rem; background: var(--light-gray); border-radius: 5px; font-size: 0.85rem; color: var(--text-secondary);">
                <strong>بيانات الدخول الافتراضية:</strong><br>
                اسم المستخدم: admin<br>
                كلمة المرور: admin123
            </div>
        </div>
    </div>
</body>
</html>

