<?php
require_once 'config.php';

// Get product slug from URL
$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    redirect(SITE_URL);
}

// Get product with category
$stmt = $pdo->prepare("SELECT p.*, c.name as category_name, c.slug as category_slug 
                       FROM products p 
                       LEFT JOIN categories c ON p.category_id = c.id 
                       WHERE p.slug = ?");
$stmt->execute([$slug]);
$product = $stmt->fetch();

if (!$product) {
    redirect(SITE_URL);
}

$page_title = $product['title'];

// Handle add to cart
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add') {
    $qty = intval($_POST['qty'] ?? 1);
    if ($qty > 0 && $qty <= $product['stock']) {
        addToCart($product['id'], $qty);
        $_SESSION['success_message'] = 'تم إضافة المنتج للسلة بنجاح';
        redirect('cart.php');
    }
}

include 'includes/header.php';
?>

<div class="container">
    <div style="margin-bottom: 2rem;">
        <?php if ($product['category_slug']): ?>
            <a href="category.php?slug=<?= $product['category_slug'] ?>" style="color: var(--primary-blue); text-decoration: none;">
                ← العودة إلى <?= htmlspecialchars($product['category_name']) ?>
            </a>
        <?php else: ?>
            <a href="<?= SITE_URL ?>" style="color: var(--primary-blue); text-decoration: none;">
                ← العودة للرئيسية
            </a>
        <?php endif; ?>
    </div>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
        <div>
            <?php if ($product['image']): ?>
                <img src="<?= UPLOAD_URL . $product['image'] ?>" 
                     alt="<?= htmlspecialchars($product['title']) ?>" 
                     style="width: 100%; border-radius: 8px;"
                     onerror="this.src='https://via.placeholder.com/500x400?text=<?= urlencode($product['title']) ?>'">
            <?php else: ?>
                <img src="https://via.placeholder.com/500x400?text=<?= urlencode($product['title']) ?>" 
                     alt="<?= htmlspecialchars($product['title']) ?>" 
                     style="width: 100%; border-radius: 8px;">
            <?php endif; ?>
        </div>

        <div>
            <h1 style="color: var(--dark-blue); margin-bottom: 1rem; font-size: 2rem;">
                <?= htmlspecialchars($product['title']) ?>
            </h1>

            <?php if ($product['category_name']): ?>
                <p style="color: var(--text-secondary); margin-bottom: 1rem;">
                    القسم: 
                    <a href="category.php?slug=<?= $product['category_slug'] ?>" style="color: var(--primary-blue); text-decoration: none;">
                        <?= htmlspecialchars($product['category_name']) ?>
                    </a>
                </p>
            <?php endif; ?>

            <div style="font-size: 2rem; color: var(--primary-blue); font-weight: bold; margin-bottom: 1.5rem;">
                <?= formatPrice($product['price']) ?>
            </div>

            <?php if ($product['description']): ?>
                <div style="color: var(--text-secondary); margin-bottom: 2rem; line-height: 1.6;">
                    <h3 style="color: var(--text-main); margin-bottom: 0.5rem;">الوصف:</h3>
                    <?= nl2br(htmlspecialchars($product['description'])) ?>
                </div>
            <?php endif; ?>

            <div style="margin-bottom: 2rem;">
                <?php if ($product['stock'] > 0): ?>
                    <p style="color: var(--success); font-weight: bold;">
                        ✓ متوفر في المخزون (<?= $product['stock'] ?> قطعة)
                    </p>
                <?php else: ?>
                    <p style="color: var(--error); font-weight: bold;">
                        ✗ غير متوفر حالياً
                    </p>
                <?php endif; ?>
            </div>

            <?php if ($product['stock'] > 0): ?>
                <form method="post" style="display: flex; gap: 1rem; align-items: center;">
                    <input type="hidden" name="action" value="add">
                    <div class="form-group" style="margin: 0;">
                        <label for="qty">الكمية:</label>
                        <input type="number" 
                               name="qty" 
                               id="qty" 
                               value="1" 
                               min="1" 
                               max="<?= $product['stock'] ?>" 
                               class="form-control" 
                               style="width: 100px;">
                    </div>
                    <button type="submit" class="btn btn-primary" style="padding: 0.75rem 2rem; font-size: 1.1rem;">
                        أضف للسلة
                    </button>
                </form>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
    @media (max-width: 768px) {
        .container > div {
            grid-template-columns: 1fr !important;
        }
    }
</style>

<?php include 'includes/footer.php'; ?>

