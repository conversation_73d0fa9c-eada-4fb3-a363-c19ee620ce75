<?php
require_once 'config.php';

$order_id = intval($_GET['order_id'] ?? 0);

if ($order_id <= 0) {
    redirect(SITE_URL);
}

// Get order details
$stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
$stmt->execute([$order_id]);
$order = $stmt->fetch();

if (!$order) {
    redirect(SITE_URL);
}

// Get order items
$stmt = $pdo->prepare("SELECT oi.*, p.title, p.image 
                       FROM order_items oi 
                       LEFT JOIN products p ON oi.product_id = p.id 
                       WHERE oi.order_id = ?");
$stmt->execute([$order_id]);
$order_items = $stmt->fetchAll();

$page_title = 'فاتورة رقم #' . $order_id;

// Generate WhatsApp message
$whatsapp_number = getSetting('whatsapp_number', '9647700000000');
$site_name = getSetting('site_name', 'Speed Store');

$message = "مرحباً، لدي طلب جديد من $site_name\n\n";
$message .= "رقم الطلب: #$order_id\n";
$message .= "الاسم: {$order['customer_name']}\n";
$message .= "الهاتف: {$order['phone']}\n";
$message .= "العنوان: {$order['address']}\n\n";
$message .= "المنتجات:\n";

foreach ($order_items as $item) {
    $message .= "- {$item['title']} × {$item['qty']} = " . formatPrice($item['price'] * $item['qty']) . "\n";
}

$message .= "\nالمجموع الكلي: " . formatPrice($order['total']);

if (!empty($order['notes'])) {
    $message .= "\n\nملاحظات: {$order['notes']}";
}

$whatsapp_link = "https://api.whatsapp.com/send?phone=$whatsapp_number&text=" . urlencode($message);

include 'includes/header.php';
?>

<div class="container">
    <div class="invoice-container">
        <div class="invoice-header">
            <h1 style="color: var(--primary-blue); margin-bottom: 0.5rem;">
                🛒 <?= getSetting('site_name', 'Speed Store') ?>
            </h1>
            <p style="color: var(--text-secondary);">
                <?= getSetting('site_address', '') ?>
            </p>
        </div>

        <div style="text-align: center; padding: 2rem 0; background: var(--light-gray); border-radius: 8px; margin-bottom: 2rem;">
            <div style="display: inline-block; background: var(--success); color: white; padding: 0.5rem 1.5rem; border-radius: 50px; margin-bottom: 1rem;">
                ✓ تم استلام طلبك بنجاح
            </div>
            <h2 style="color: var(--dark-blue); margin: 0;">
                فاتورة رقم #<?= $order_id ?>
            </h2>
        </div>

        <div class="invoice-details" style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
            <div>
                <h3 style="color: var(--dark-blue); margin-bottom: 1rem;">معلومات العميل</h3>
                <p><strong>الاسم:</strong> <?= htmlspecialchars($order['customer_name']) ?></p>
                <p><strong>الهاتف:</strong> <?= htmlspecialchars($order['phone']) ?></p>
                <p><strong>العنوان:</strong> <?= nl2br(htmlspecialchars($order['address'])) ?></p>
                <?php if (!empty($order['notes'])): ?>
                    <p><strong>ملاحظات:</strong> <?= nl2br(htmlspecialchars($order['notes'])) ?></p>
                <?php endif; ?>
            </div>
            <div>
                <h3 style="color: var(--dark-blue); margin-bottom: 1rem;">معلومات الطلب</h3>
                <p><strong>رقم الطلب:</strong> #<?= $order_id ?></p>
                <p><strong>التاريخ:</strong> <?= date('Y-m-d H:i', strtotime($order['created_at'])) ?></p>
                <p><strong>الحالة:</strong> 
                    <span style="background: var(--warning); color: white; padding: 0.25rem 0.75rem; border-radius: 3px;">
                        قيد المعالجة
                    </span>
                </p>
            </div>
        </div>

        <h3 style="color: var(--dark-blue); margin-bottom: 1rem;">تفاصيل المنتجات</h3>
        <table class="invoice-table">
            <thead>
                <tr style="background: var(--light-gray);">
                    <th>المنتج</th>
                    <th>السعر</th>
                    <th>الكمية</th>
                    <th>المجموع</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($order_items as $item): ?>
                    <tr>
                        <td>
                            <div style="display: flex; align-items: center; gap: 1rem;">
                                <?php if ($item['image']): ?>
                                    <img src="<?= UPLOAD_URL . $item['image'] ?>" 
                                         alt="<?= htmlspecialchars($item['title']) ?>" 
                                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 5px;"
                                         onerror="this.src='https://via.placeholder.com/50x50'">
                                <?php else: ?>
                                    <img src="https://via.placeholder.com/50x50" 
                                         alt="<?= htmlspecialchars($item['title']) ?>" 
                                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 5px;">
                                <?php endif; ?>
                                <span><?= htmlspecialchars($item['title']) ?></span>
                            </div>
                        </td>
                        <td><?= formatPrice($item['price']) ?></td>
                        <td><?= $item['qty'] ?></td>
                        <td><strong><?= formatPrice($item['price'] * $item['qty']) ?></strong></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div style="text-align: left; margin-top: 2rem; padding: 1.5rem; background: var(--light-gray); border-radius: 8px;">
            <div class="invoice-total">
                المجموع الكلي: <?= formatPrice($order['total']) ?>
            </div>
        </div>

        <div style="text-align: center; margin-top: 2rem; padding: 2rem; background: #e7f3ff; border-radius: 8px;">
            <h3 style="color: var(--dark-blue); margin-bottom: 1rem;">
                أرسل طلبك عبر واتساب
            </h3>
            <p style="color: var(--text-secondary); margin-bottom: 1.5rem;">
                اضغط على الزر أدناه لإرسال تفاصيل طلبك إلى المتجر عبر واتساب
            </p>
            <a href="<?= $whatsapp_link ?>" 
               target="_blank" 
               class="btn btn-success" 
               style="padding: 1rem 3rem; font-size: 1.2rem; text-decoration: none; display: inline-block;">
                📱 إرسال عبر واتساب
            </a>
        </div>

        <div style="text-align: center; margin-top: 2rem;">
            <a href="<?= SITE_URL ?>" class="btn btn-primary">
                العودة للرئيسية
            </a>
            <button onclick="window.print()" class="btn btn-secondary" style="margin-right: 1rem;">
                🖨️ طباعة الفاتورة
            </button>
        </div>
    </div>
</div>

<style>
    @media print {
        .site-header,
        .site-footer,
        .btn {
            display: none !important;
        }
        
        .invoice-container {
            box-shadow: none !important;
        }
    }
    
    @media (max-width: 768px) {
        .invoice-details {
            grid-template-columns: 1fr !important;
        }
    }
</style>

<?php include 'includes/footer.php'; ?>

