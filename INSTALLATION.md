# 📦 دليل التثبيت السريع - Speed Store

## خطوات التثبيت

### 1️⃣ متطلبات التشغيل
تأكد من توفر:
- ✅ PHP 7.4 أو أحدث
- ✅ MySQL 5.7 أو أحدث
- ✅ Apache مع mod_rewrite
- ✅ مكتبة GD لمعالجة الصور

---

### 2️⃣ رفع الملفات
1. قم بتحميل جميع ملفات المشروع
2. ارفعها إلى مجلد الموقع على السيرفر (مثلاً: `public_html` أو `www`)

---

### 3️⃣ إنشاء قاعدة البيانات

#### باستخدام phpMyAdmin:
1. افتح phpMyAdmin من لوحة تحكم الاستضافة
2. اضغط على **Import** (استيراد)
3. اختر ملف `database.sql`
4. اضغط **Go** (تنفيذ)

#### باستخدام سطر الأوامر:
```bash
mysql -u username -p < database.sql
```

---

### 4️⃣ تعديل ملف الإعدادات

افتح ملف `config.php` وعدّل البيانات التالية:

```php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');           // عادة localhost
define('DB_USER', 'your_db_username');    // اسم مستخدم قاعدة البيانات
define('DB_PASS', 'your_db_password');    // كلمة مرور قاعدة البيانات
define('DB_NAME', 'speed_store');         // اسم قاعدة البيانات

// رابط الموقع
define('SITE_URL', 'http://yoursite.com'); // غيّره لرابط موقعك
```

**مثال للاستضافة المحلية:**
```php
define('SITE_URL', 'http://localhost/speed_store');
```

**مثال للاستضافة الحقيقية:**
```php
define('SITE_URL', 'https://www.yourstore.com');
```

---

### 5️⃣ ضبط صلاحيات المجلدات

تأكد من أن مجلد `uploads/` له صلاحيات الكتابة:

#### على Linux/Unix:
```bash
chmod 755 uploads/
```

#### على Windows:
- انقر بزر الماوس الأيمن على مجلد `uploads`
- اختر **Properties** > **Security**
- تأكد من أن المستخدم له صلاحيات الكتابة

---

### 6️⃣ الوصول للموقع

#### الموقع الرئيسي:
```
http://yoursite.com
```

#### لوحة التحكم:
```
http://yoursite.com/admin
```

---

### 7️⃣ تسجيل الدخول

استخدم البيانات الافتراضية:

```
اسم المستخدم: admin
كلمة المرور: admin123
```

⚠️ **مهم جداً:** قم بتغيير كلمة المرور فوراً!

---

## ⚙️ الإعدادات الأولية

بعد تسجيل الدخول:

### 1. تحديث إعدادات المتجر
1. اذهب إلى **الإعدادات**
2. أدخل:
   - اسم المتجر
   - رقم واتساب (مثال: `9647700000000`)
   - عنوان المتجر
   - البريد الإلكتروني

### 2. إضافة الأقسام
1. اذهب إلى **الأقسام**
2. أضف أقسام المنتجات (مثل: إلكترونيات، ملابس، إلخ)

### 3. إضافة المنتجات
1. اذهب إلى **المنتجات**
2. أضف منتجاتك مع:
   - الاسم
   - القسم
   - السعر
   - المخزون
   - الصورة
   - الوصف

---

## 🔧 استكشاف الأخطاء

### ❌ خطأ: "Could not connect to database"
**الحل:**
- تأكد من صحة بيانات الاتصال في `config.php`
- تأكد من أن MySQL يعمل
- تأكد من أن قاعدة البيانات موجودة

### ❌ الصور لا تظهر
**الحل:**
- تأكد من صلاحيات مجلد `uploads/`
- تأكد من صحة `SITE_URL` في `config.php`
- تأكد من رفع الصور بنجاح

### ❌ خطأ 404 في الصفحات
**الحل:**
- تأكد من تفعيل `mod_rewrite` في Apache
- تأكد من وجود ملف `.htaccess`

### ❌ لا يمكن رفع الصور
**الحل:**
- تأكد من صلاحيات مجلد `uploads/`
- تأكد من إعدادات PHP:
  ```php
  upload_max_filesize = 10M
  post_max_size = 10M
  ```

---

## 🔒 تأمين الموقع

### 1. تغيير كلمة المرور
قم بتحديث كلمة المرور في قاعدة البيانات:

```sql
UPDATE admin_users 
SET password = '$2y$10$NEW_HASHED_PASSWORD' 
WHERE username = 'admin';
```

لتوليد كلمة مرور مشفرة، استخدم:
```php
<?php
echo password_hash('your_new_password', PASSWORD_DEFAULT);
?>
```

### 2. حماية ملف config.php
تأكد من أن ملف `.htaccess` يحمي `config.php`

### 3. استخدام HTTPS
إذا كان لديك شهادة SSL، فعّل HTTPS في `.htaccess`

---

## 📱 إعداد واتساب

### تنسيق رقم واتساب:
```
الصيغة: [رمز الدولة][رقم الهاتف]
مثال للعراق: 9647700000000
مثال للسعودية: 966500000000
مثال لمصر: 201000000000
```

**ملاحظة:** بدون علامة + أو 00

---

## 🎨 التخصيص

### تغيير الألوان:
عدّل ملف `assets/css/style.css`:

```css
:root {
    --primary-blue: #0869d0;    /* اللون الأساسي */
    --dark-blue: #064a91;       /* لون الهيدر */
    /* ... */
}
```

### إضافة شعار:
عدّل ملف `includes/header.php` واستبدل:
```php
<a href="<?= SITE_URL ?>" class="site-logo">
    🛒 <?= $site_name ?>
</a>
```

بـ:
```php
<a href="<?= SITE_URL ?>" class="site-logo">
    <img src="<?= SITE_URL ?>/uploads/logo.png" alt="<?= $site_name ?>">
</a>
```

---

## 📊 النسخ الاحتياطي

### نسخ احتياطي لقاعدة البيانات:
```bash
mysqldump -u username -p speed_store > backup.sql
```

### نسخ احتياطي للملفات:
قم بنسخ:
- مجلد `uploads/` (الصور)
- ملف `config.php` (الإعدادات)

---

## 🆘 الدعم الفني

إذا واجهت أي مشكلة:
1. راجع قسم **استكشاف الأخطاء** أعلاه
2. تحقق من ملف `error_log` في السيرفر
3. تأكد من تفعيل عرض الأخطاء في PHP للتطوير:
   ```php
   error_reporting(E_ALL);
   ini_set('display_errors', 1);
   ```

---

## ✅ قائمة التحقق النهائية

- [ ] تم رفع جميع الملفات
- [ ] تم إنشاء قاعدة البيانات
- [ ] تم تعديل `config.php`
- [ ] مجلد `uploads/` له صلاحيات الكتابة
- [ ] تم تسجيل الدخول بنجاح
- [ ] تم تغيير كلمة المرور الافتراضية
- [ ] تم إضافة إعدادات المتجر
- [ ] تم إضافة الأقسام والمنتجات
- [ ] تم اختبار عملية الشراء
- [ ] تم اختبار إرسال واتساب

---

**🎉 مبروك! متجرك جاهز الآن!**

