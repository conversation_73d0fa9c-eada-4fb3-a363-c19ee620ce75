# 🤝 دليل المساهمة - Speed Store

شكراً لاهتمامك بالمساهمة في Speed Store! نرحب بجميع أنواع المساهمات.

---

## 📋 جدول المحتويات

1. [كيف يمكنني المساهمة؟](#كيف-يمكنني-المساهمة)
2. [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
3. [اقتراح ميزات جديدة](#اقتراح-ميزات-جديدة)
4. [المساهمة بالكود](#المساهمة-بالكود)
5. [معايير الكود](#معايير-الكود)
6. [عملية المراجعة](#عملية-المراجعة)

---

## 🎯 كيف يمكنني المساهمة؟

هناك عدة طرق للمساهمة:

### 1. الإبلاغ عن الأخطاء 🐛
- وجدت خطأ؟ أخبرنا عنه!
- افتح Issue جديد مع وصف تفصيلي

### 2. اقتراح ميزات جديدة 💡
- لديك فكرة رائعة؟ شاركها معنا!
- افتح Issue مع وصف الميزة المقترحة

### 3. تحسين الوثائق 📚
- وجدت خطأ إملائي؟
- تريد إضافة توضيح؟
- أرسل Pull Request

### 4. كتابة الكود 💻
- إصلاح أخطاء
- إضافة ميزات جديدة
- تحسين الأداء

### 5. الترجمة 🌍
- ترجمة الواجهة للغات أخرى
- ترجمة الوثائق

### 6. التصميم 🎨
- تحسين التصميم
- إضافة أيقونات
- تحسين تجربة المستخدم

---

## 🐛 الإبلاغ عن الأخطاء

### قبل الإبلاغ:
1. ✅ تأكد من أنك تستخدم آخر إصدار
2. ✅ ابحث في Issues المفتوحة
3. ✅ تأكد من أنه خطأ فعلي وليس خطأ في الإعدادات

### عند الإبلاغ، قدم:

#### معلومات البيئة:
```
- نظام التشغيل: (Windows/Linux/Mac)
- إصدار PHP: (7.4/8.0/8.1)
- إصدار MySQL: (5.7/8.0)
- المتصفح: (Chrome/Firefox/Safari)
```

#### وصف الخطأ:
- ما الذي حدث؟
- ما الذي كنت تتوقعه؟
- متى يحدث الخطأ؟

#### خطوات إعادة الإنتاج:
```
1. اذهب إلى '...'
2. اضغط على '...'
3. انتقل إلى '...'
4. شاهد الخطأ
```

#### لقطات شاشة:
- أضف لقطات شاشة إن أمكن

#### رسائل الخطأ:
```
نسخ رسالة الخطأ الكاملة هنا
```

### مثال على تقرير خطأ جيد:

```markdown
## وصف الخطأ
عند محاولة إضافة منتج للسلة، تظهر رسالة خطأ ولا يتم إضافة المنتج.

## البيئة
- نظام التشغيل: Windows 10
- PHP: 8.0
- MySQL: 8.0
- المتصفح: Chrome 120

## خطوات إعادة الإنتاج
1. افتح صفحة منتج
2. اضغط "أضف للسلة"
3. تظهر رسالة خطأ

## النتيجة المتوقعة
يجب أن يتم إضافة المنتج للسلة

## النتيجة الفعلية
تظهر رسالة: "Error: Undefined variable $product_id"

## لقطة شاشة
[صورة]
```

---

## 💡 اقتراح ميزات جديدة

### قبل الاقتراح:
1. ✅ تحقق من FEATURES.md
2. ✅ ابحث في Issues المفتوحة
3. ✅ تأكد من أن الميزة مفيدة للجميع

### عند الاقتراح، قدم:

#### وصف الميزة:
- ما هي الميزة المقترحة؟
- لماذا هي مفيدة؟
- من سيستفيد منها؟

#### أمثلة على الاستخدام:
```
كمستخدم، أريد أن [فعل شيء]
حتى أتمكن من [تحقيق هدف]
```

#### تصميم مقترح (اختياري):
- رسم توضيحي
- لقطة شاشة من موقع آخر
- Wireframe

### مثال على اقتراح ميزة جيد:

```markdown
## الميزة المقترحة
إضافة بحث في المنتجات

## الوصف
أريد إضافة حقل بحث في الهيدر يسمح للزوار بالبحث عن المنتجات بالاسم.

## الفائدة
- تسهيل إيجاد المنتجات
- تحسين تجربة المستخدم
- زيادة المبيعات

## أمثلة الاستخدام
1. الزائر يكتب "هاتف" في حقل البحث
2. تظهر جميع المنتجات التي تحتوي على كلمة "هاتف"
3. الزائر يضغط على المنتج المطلوب

## تصميم مقترح
[صورة أو رسم]
```

---

## 💻 المساهمة بالكود

### 1. Fork المشروع
```bash
# اضغط على زر Fork في GitHub
```

### 2. Clone المشروع
```bash
git clone https://github.com/YOUR_USERNAME/speed_store.git
cd speed_store
```

### 3. إنشاء Branch جديد
```bash
git checkout -b feature/amazing-feature
```

**تسمية Branches:**
- `feature/` للميزات الجديدة
- `fix/` لإصلاح الأخطاء
- `docs/` للوثائق
- `style/` للتصميم
- `refactor/` لإعادة الهيكلة

### 4. إجراء التغييرات
- اكتب كود نظيف
- اتبع معايير الكود
- أضف تعليقات واضحة

### 5. Commit التغييرات
```bash
git add .
git commit -m "Add: وصف واضح للتغيير"
```

**تنسيق Commit Messages:**
```
Add: إضافة ميزة جديدة
Fix: إصلاح خطأ
Update: تحديث ميزة موجودة
Remove: حذف شيء
Docs: تحديث الوثائق
Style: تحسين التصميم
Refactor: إعادة هيكلة الكود
```

### 6. Push للـ Branch
```bash
git push origin feature/amazing-feature
```

### 7. فتح Pull Request
- اذهب إلى GitHub
- اضغط "New Pull Request"
- املأ النموذج بوضوح

---

## 📏 معايير الكود

### PHP

#### التنسيق:
```php
<?php
// استخدم 4 مسافات للإزاحة
// استخدم أسماء واضحة للمتغيرات
// أضف تعليقات للكود المعقد

function getUserById($id) {
    // التحقق من المدخلات
    if (!is_numeric($id)) {
        return false;
    }
    
    // استعلام قاعدة البيانات
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$id]);
    
    return $stmt->fetch();
}
?>
```

#### الأمان:
```php
// ✅ استخدم Prepared Statements
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$id]);

// ❌ لا تستخدم استعلامات مباشرة
$query = "SELECT * FROM users WHERE id = $id"; // خطر!

// ✅ استخدم htmlspecialchars للعرض
echo htmlspecialchars($user['name']);

// ❌ لا تعرض البيانات مباشرة
echo $user['name']; // خطر XSS!
```

### CSS

#### التنسيق:
```css
/* استخدم أسماء واضحة للـ classes */
.product-card {
    background: white;
    padding: 1rem;
    border-radius: 8px;
}

/* استخدم متغيرات CSS */
:root {
    --primary-color: #0869d0;
}

.button {
    background: var(--primary-color);
}
```

### JavaScript

#### التنسيق:
```javascript
// استخدم const و let بدلاً من var
const API_URL = 'https://api.example.com';
let counter = 0;

// استخدم arrow functions
const addToCart = (productId) => {
    // الكود هنا
};

// أضف تعليقات
// هذه الدالة تضيف منتج للسلة
function addProduct(id) {
    // ...
}
```

### SQL

#### التنسيق:
```sql
-- استخدم أحرف كبيرة للكلمات المفتاحية
SELECT 
    p.id,
    p.title,
    c.name AS category_name
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
WHERE p.stock > 0
ORDER BY p.created_at DESC;

-- أضف تعليقات للاستعلامات المعقدة
-- هذا الاستعلام يجلب المنتجات المتوفرة مع أسماء الأقسام
```

---

## 🔍 عملية المراجعة

### ما نبحث عنه:

#### 1. الوظيفة ✅
- هل الكود يعمل؟
- هل يحل المشكلة؟
- هل تم اختباره؟

#### 2. الجودة 📊
- هل الكود نظيف؟
- هل يتبع المعايير؟
- هل التعليقات واضحة؟

#### 3. الأمان 🔒
- هل هناك ثغرات أمنية؟
- هل تم التحقق من المدخلات؟
- هل تم استخدام Prepared Statements؟

#### 4. الأداء ⚡
- هل الكود محسّن؟
- هل الاستعلامات فعالة؟
- هل هناك تكرار غير ضروري؟

#### 5. التوافق 🔄
- هل يعمل على جميع المتصفحات؟
- هل يعمل على جميع الأجهزة؟
- هل متوافق مع الإصدارات القديمة؟

### مدة المراجعة:
- عادة 1-3 أيام
- قد تستغرق أكثر للتغييرات الكبيرة

### بعد المراجعة:
- قد نطلب تعديلات
- قد نقبل مباشرة
- قد نرفض مع توضيح السبب

---

## ✅ قائمة التحقق

قبل إرسال Pull Request، تأكد من:

- [ ] الكود يعمل بدون أخطاء
- [ ] تم اختبار جميع الحالات
- [ ] تم اتباع معايير الكود
- [ ] تم إضافة تعليقات واضحة
- [ ] تم تحديث الوثائق (إن لزم)
- [ ] لا توجد ملفات غير ضرورية
- [ ] Commit messages واضحة
- [ ] تم حل جميع Conflicts

---

## 🎓 نصائح للمساهمين الجدد

### ابدأ صغيراً:
- إصلاح خطأ إملائي
- تحسين تعليق
- إضافة مثال في الوثائق

### اطلب المساعدة:
- لا تتردد في السؤال
- افتح Issue للنقاش
- تواصل مع الفريق

### كن صبوراً:
- المراجعة تأخذ وقتاً
- قد نطلب تعديلات
- التعلم يحتاج وقتاً

---

## 📞 التواصل

- **GitHub Issues**: للأخطاء والاقتراحات
- **Pull Requests**: للمساهمات بالكود
- **Discussions**: للنقاشات العامة

---

## 🙏 شكراً

شكراً لمساهمتك في جعل Speed Store أفضل! 

كل مساهمة، مهما كانت صغيرة، تُحدث فرقاً.

---

**صُنع بـ ❤️ من قبل المجتمع**

