<?php
require_once 'config.php';

$page_title = 'إتمام الطلب';

// Check if cart is empty
$cart = getCart();
if (empty($cart)) {
    redirect('cart.php');
}

// Get cart items
$ids = array_keys($cart);
$placeholders = implode(',', array_fill(0, count($ids), '?'));
$stmt = $pdo->prepare("SELECT * FROM products WHERE id IN ($placeholders)");
$stmt->execute($ids);
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

$cart_items = [];
$total = 0;

foreach ($products as $product) {
    $qty = $cart[$product['id']];
    $subtotal = $product['price'] * $qty;
    $total += $subtotal;
    
    $cart_items[] = [
        'product' => $product,
        'qty' => $qty,
        'subtotal' => $subtotal
    ];
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $customer_name = trim($_POST['customer_name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    
    $errors = [];
    
    if (empty($customer_name)) {
        $errors[] = 'الرجاء إدخال الاسم';
    }
    
    if (empty($phone)) {
        $errors[] = 'الرجاء إدخال رقم الهاتف';
    }
    
    if (empty($address)) {
        $errors[] = 'الرجاء إدخال العنوان';
    }
    
    if (empty($errors)) {
        try {
            $pdo->beginTransaction();
            
            // Create order
            $stmt = $pdo->prepare("INSERT INTO orders (customer_name, phone, address, notes, total, status) 
                                   VALUES (?, ?, ?, ?, ?, 'pending')");
            $stmt->execute([$customer_name, $phone, $address, $notes, $total]);
            $order_id = $pdo->lastInsertId();
            
            // Add order items
            $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_id, qty, price) VALUES (?, ?, ?, ?)");
            
            foreach ($cart_items as $item) {
                $stmt->execute([
                    $order_id,
                    $item['product']['id'],
                    $item['qty'],
                    $item['product']['price']
                ]);
                
                // Update stock
                $update_stmt = $pdo->prepare("UPDATE products SET stock = stock - ? WHERE id = ?");
                $update_stmt->execute([$item['qty'], $item['product']['id']]);
            }
            
            $pdo->commit();
            
            // Clear cart
            clearCart();
            
            // Redirect to invoice
            redirect("invoice.php?order_id=$order_id");
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $errors[] = 'حدث خطأ أثناء معالجة الطلب. الرجاء المحاولة مرة أخرى.';
        }
    }
}

include 'includes/header.php';
?>

<div class="container">
    <h1 style="color: var(--dark-blue); margin-bottom: 2rem; font-size: 2.5rem;">
        إتمام الطلب
    </h1>

    <?php if (!empty($errors)): ?>
        <div class="alert alert-error">
            <ul style="margin: 0; padding-right: 1.5rem;">
                <?php foreach ($errors as $error): ?>
                    <li><?= $error ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
        <!-- Order Form -->
        <div style="background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <h2 style="color: var(--dark-blue); margin-bottom: 1.5rem;">معلومات الطلب</h2>
            
            <form method="post">
                <div class="form-group">
                    <label for="customer_name">الاسم الكامل *</label>
                    <input type="text" 
                           name="customer_name" 
                           id="customer_name" 
                           class="form-control" 
                           value="<?= htmlspecialchars($_POST['customer_name'] ?? '') ?>"
                           required>
                </div>

                <div class="form-group">
                    <label for="phone">رقم الهاتف *</label>
                    <input type="tel" 
                           name="phone" 
                           id="phone" 
                           class="form-control" 
                           value="<?= htmlspecialchars($_POST['phone'] ?? '') ?>"
                           placeholder="07XXXXXXXXX"
                           required>
                </div>

                <div class="form-group">
                    <label for="address">العنوان *</label>
                    <textarea name="address" 
                              id="address" 
                              class="form-control" 
                              rows="3" 
                              required><?= htmlspecialchars($_POST['address'] ?? '') ?></textarea>
                </div>

                <div class="form-group">
                    <label for="notes">ملاحظات إضافية</label>
                    <textarea name="notes" 
                              id="notes" 
                              class="form-control" 
                              rows="3"><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                </div>

                <button type="submit" class="btn btn-primary" style="width: 100%; padding: 1rem; font-size: 1.1rem;">
                    تأكيد الطلب
                </button>
            </form>
        </div>

        <!-- Order Summary -->
        <div style="background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <h2 style="color: var(--dark-blue); margin-bottom: 1.5rem;">ملخص الطلب</h2>
            
            <div style="margin-bottom: 1.5rem;">
                <?php foreach ($cart_items as $item): ?>
                    <div style="display: flex; justify-content: space-between; padding: 0.75rem 0; border-bottom: 1px solid var(--light-gray);">
                        <div>
                            <strong><?= htmlspecialchars($item['product']['title']) ?></strong>
                            <br>
                            <small style="color: var(--text-secondary);">
                                <?= $item['qty'] ?> × <?= formatPrice($item['product']['price']) ?>
                            </small>
                        </div>
                        <div style="font-weight: bold; color: var(--primary-blue);">
                            <?= formatPrice($item['subtotal']) ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <div style="background: var(--light-gray); padding: 1rem; border-radius: 5px; margin-top: 1.5rem;">
                <div style="display: flex; justify-content: space-between; font-size: 1.3rem;">
                    <strong>المجموع الكلي:</strong>
                    <strong style="color: var(--primary-blue);"><?= formatPrice($total) ?></strong>
                </div>
            </div>

            <div style="margin-top: 1.5rem; padding: 1rem; background: #e7f3ff; border-radius: 5px;">
                <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;">
                    ℹ️ سيتم التواصل معك لتأكيد الطلب وترتيب التوصيل
                </p>
            </div>
        </div>
    </div>
</div>

<style>
    @media (max-width: 768px) {
        .container > div {
            grid-template-columns: 1fr !important;
        }
    }
</style>

<?php include 'includes/footer.php'; ?>

