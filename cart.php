<?php
require_once 'config.php';

$page_title = 'السلة';

// Handle cart actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add') {
        $product_id = intval($_POST['product_id'] ?? 0);
        $qty = intval($_POST['qty'] ?? 1);
        
        if ($product_id > 0 && $qty > 0) {
            // Check stock
            $stmt = $pdo->prepare("SELECT stock FROM products WHERE id = ?");
            $stmt->execute([$product_id]);
            $product = $stmt->fetch();
            
            if ($product && $product['stock'] >= $qty) {
                addToCart($product_id, $qty);
                $_SESSION['success_message'] = 'تم إضافة المنتج للسلة بنجاح';
            } else {
                $_SESSION['error_message'] = 'الكمية المطلوبة غير متوفرة';
            }
        }
        redirect('cart.php');
    }
    
    if ($action === 'update') {
        $product_id = intval($_POST['product_id'] ?? 0);
        $qty = intval($_POST['qty'] ?? 0);
        
        if ($product_id > 0) {
            updateCartQty($product_id, $qty);
            $_SESSION['success_message'] = 'تم تحديث السلة';
        }
        redirect('cart.php');
    }
    
    if ($action === 'remove') {
        $product_id = intval($_POST['product_id'] ?? 0);
        
        if ($product_id > 0) {
            removeFromCart($product_id);
            $_SESSION['success_message'] = 'تم حذف المنتج من السلة';
        }
        redirect('cart.php');
    }
    
    if ($action === 'clear') {
        clearCart();
        $_SESSION['success_message'] = 'تم تفريغ السلة';
        redirect('cart.php');
    }
}

// Get cart items with product details
$cart = getCart();
$cart_items = [];
$total = 0;

if (!empty($cart)) {
    $ids = array_keys($cart);
    $placeholders = implode(',', array_fill(0, count($ids), '?'));
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id IN ($placeholders)");
    $stmt->execute($ids);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($products as $product) {
        $qty = $cart[$product['id']];
        $subtotal = $product['price'] * $qty;
        $total += $subtotal;
        
        $cart_items[] = [
            'product' => $product,
            'qty' => $qty,
            'subtotal' => $subtotal
        ];
    }
}

include 'includes/header.php';
?>

<div class="container">
    <h1 style="color: var(--dark-blue); margin-bottom: 2rem; font-size: 2.5rem;">
        سلة المشتريات
    </h1>

    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success">
            <?= $_SESSION['success_message'] ?>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-error">
            <?= $_SESSION['error_message'] ?>
        </div>
        <?php unset($_SESSION['error_message']); ?>
    <?php endif; ?>

    <?php if (empty($cart_items)): ?>
        <div class="alert alert-info">
            السلة فارغة. <a href="<?= SITE_URL ?>" style="color: var(--primary-blue);">تصفح المنتجات</a>
        </div>
    <?php else: ?>
        <table class="cart-table">
            <thead>
                <tr>
                    <th>المنتج</th>
                    <th>السعر</th>
                    <th>الكمية</th>
                    <th>المجموع</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($cart_items as $item): ?>
                    <tr>
                        <td>
                            <div style="display: flex; align-items: center; gap: 1rem;">
                                <?php if ($item['product']['image']): ?>
                                    <img src="<?= UPLOAD_URL . $item['product']['image'] ?>" 
                                         alt="<?= htmlspecialchars($item['product']['title']) ?>" 
                                         style="width: 60px; height: 60px; object-fit: cover; border-radius: 5px;"
                                         onerror="this.src='https://via.placeholder.com/60x60'">
                                <?php else: ?>
                                    <img src="https://via.placeholder.com/60x60" 
                                         alt="<?= htmlspecialchars($item['product']['title']) ?>" 
                                         style="width: 60px; height: 60px; object-fit: cover; border-radius: 5px;">
                                <?php endif; ?>
                                <div>
                                    <strong><?= htmlspecialchars($item['product']['title']) ?></strong>
                                </div>
                            </div>
                        </td>
                        <td><?= formatPrice($item['product']['price']) ?></td>
                        <td>
                            <form method="post" style="display: inline-block;">
                                <input type="hidden" name="action" value="update">
                                <input type="hidden" name="product_id" value="<?= $item['product']['id'] ?>">
                                <input type="number" 
                                       name="qty" 
                                       value="<?= $item['qty'] ?>" 
                                       min="1" 
                                       max="<?= $item['product']['stock'] ?>"
                                       style="width: 70px; padding: 0.25rem; border: 1px solid #ddd; border-radius: 3px;"
                                       onchange="this.form.submit()">
                            </form>
                        </td>
                        <td><strong><?= formatPrice($item['subtotal']) ?></strong></td>
                        <td>
                            <form method="post" style="display: inline-block;">
                                <input type="hidden" name="action" value="remove">
                                <input type="hidden" name="product_id" value="<?= $item['product']['id'] ?>">
                                <button type="submit" class="btn btn-danger" style="padding: 0.25rem 0.75rem;">
                                    حذف
                                </button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div class="cart-total">
            <h3>المجموع الكلي: <?= formatPrice($total) ?></h3>
        </div>

        <div style="display: flex; justify-content: space-between; margin-top: 2rem;">
            <form method="post">
                <input type="hidden" name="action" value="clear">
                <button type="submit" class="btn btn-secondary">
                    تفريغ السلة
                </button>
            </form>
            
            <div style="display: flex; gap: 1rem;">
                <a href="<?= SITE_URL ?>" class="btn btn-secondary">
                    متابعة التسوق
                </a>
                <a href="checkout.php" class="btn btn-primary" style="padding: 0.75rem 2rem; font-size: 1.1rem;">
                    إتمام الطلب
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>

