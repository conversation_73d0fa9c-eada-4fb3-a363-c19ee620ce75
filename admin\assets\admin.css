/* Admin Panel Styles */

.admin-layout {
    display: flex;
    min-height: 100vh;
}

.admin-sidebar {
    width: 250px;
    background-color: var(--dark-blue);
    color: var(--text-on-dark);
    padding: 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
}

.admin-logo {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.admin-logo h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
}

.admin-logo p {
    margin: 0;
    color: var(--badge-highlight);
    font-size: 0.9rem;
}

.admin-nav {
    padding: 1rem 0;
}

.admin-nav a {
    display: block;
    padding: 1rem 1.5rem;
    color: var(--text-on-dark);
    text-decoration: none;
    transition: all 0.3s;
    border-right: 3px solid transparent;
}

.admin-nav a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-right-color: var(--badge-highlight);
}

.admin-nav a.active {
    background-color: var(--primary-blue);
    border-right-color: var(--badge-highlight);
}

.admin-user {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: absolute;
    bottom: 0;
    width: 100%;
    background-color: var(--dark-blue);
}

.admin-user p {
    margin: 0;
    font-size: 0.9rem;
}

.admin-content {
    flex: 1;
    margin-right: 250px;
    padding: 2rem;
    background-color: var(--light-gray);
    min-height: 100vh;
}

.admin-header {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.admin-header h1 {
    margin: 0;
    color: var(--dark-blue);
}

.admin-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.admin-card h2 {
    margin: 0 0 1rem 0;
    color: var(--dark-blue);
    font-size: 1.3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-card .stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    color: var(--text-secondary);
    font-size: 1rem;
}

.admin-table {
    width: 100%;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--light-gray);
}

.admin-table th {
    background-color: var(--dark-blue);
    color: white;
    font-weight: 600;
}

.admin-table tr:hover {
    background-color: var(--light-gray);
}

.admin-table img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 5px;
}

.badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 3px;
    font-size: 0.85rem;
    font-weight: 600;
}

.badge-success {
    background-color: var(--success);
    color: white;
}

.badge-warning {
    background-color: var(--warning);
    color: white;
}

.badge-danger {
    background-color: var(--error);
    color: white;
}

.badge-info {
    background-color: var(--primary-blue);
    color: white;
}

.btn-group {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.85rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
}

.login-box {
    background: white;
    padding: 3rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    width: 100%;
    max-width: 400px;
}

.login-box h1 {
    text-align: center;
    color: var(--dark-blue);
    margin-bottom: 2rem;
}

.login-box .btn {
    width: 100%;
    padding: 1rem;
    font-size: 1.1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .admin-sidebar {
        width: 100%;
        position: relative;
        height: auto;
    }
    
    .admin-content {
        margin-right: 0;
    }
    
    .admin-user {
        position: relative;
    }
}

