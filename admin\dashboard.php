<?php
$page_title = 'لوحة المعلومات';
include 'includes/header.php';

// Get statistics
$stats = [];

// Total products
$stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
$stats['products'] = $stmt->fetch()['count'];

// Total categories
$stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
$stats['categories'] = $stmt->fetch()['count'];

// Total orders
$stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
$stats['orders'] = $stmt->fetch()['count'];

// Pending orders
$stmt = $pdo->query("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
$stats['pending_orders'] = $stmt->fetch()['count'];

// Total revenue
$stmt = $pdo->query("SELECT SUM(total) as total FROM orders");
$stats['revenue'] = $stmt->fetch()['total'] ?? 0;

// Recent orders
$stmt = $pdo->query("SELECT * FROM orders ORDER BY created_at DESC LIMIT 5");
$recent_orders = $stmt->fetchAll();

// Low stock products
$stmt = $pdo->query("SELECT * FROM products WHERE stock < 5 ORDER BY stock ASC LIMIT 5");
$low_stock = $stmt->fetchAll();
?>

<div class="admin-header">
    <h1>لوحة المعلومات</h1>
</div>

<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value"><?= $stats['products'] ?></div>
        <div class="stat-label">إجمالي المنتجات</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-value"><?= $stats['categories'] ?></div>
        <div class="stat-label">الأقسام</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-value"><?= $stats['orders'] ?></div>
        <div class="stat-label">إجمالي الطلبات</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-value" style="color: var(--warning);"><?= $stats['pending_orders'] ?></div>
        <div class="stat-label">طلبات قيد المعالجة</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-value" style="color: var(--success);"><?= formatPrice($stats['revenue']) ?></div>
        <div class="stat-label">إجمالي المبيعات</div>
    </div>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem;">
    <!-- Recent Orders -->
    <div class="admin-card">
        <h2>أحدث الطلبات</h2>
        <?php if (empty($recent_orders)): ?>
            <p style="color: var(--text-secondary);">لا توجد طلبات</p>
        <?php else: ?>
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>العميل</th>
                        <th>المبلغ</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_orders as $order): ?>
                        <tr>
                            <td><a href="orders.php?view=<?= $order['id'] ?>" style="color: var(--primary-blue);">#<?= $order['id'] ?></a></td>
                            <td><?= htmlspecialchars($order['customer_name']) ?></td>
                            <td><?= formatPrice($order['total']) ?></td>
                            <td>
                                <?php if ($order['status'] === 'pending'): ?>
                                    <span class="badge badge-warning">قيد المعالجة</span>
                                <?php elseif ($order['status'] === 'completed'): ?>
                                    <span class="badge badge-success">مكتمل</span>
                                <?php elseif ($order['status'] === 'cancelled'): ?>
                                    <span class="badge badge-danger">ملغي</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <div style="text-align: center; margin-top: 1rem;">
                <a href="orders.php" class="btn btn-primary btn-sm">عرض جميع الطلبات</a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Low Stock Products -->
    <div class="admin-card">
        <h2>منتجات قليلة المخزون</h2>
        <?php if (empty($low_stock)): ?>
            <p style="color: var(--text-secondary);">جميع المنتجات متوفرة</p>
        <?php else: ?>
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>المخزون</th>
                        <th>إجراء</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($low_stock as $product): ?>
                        <tr>
                            <td><?= htmlspecialchars($product['title']) ?></td>
                            <td>
                                <span class="badge badge-danger"><?= $product['stock'] ?> قطعة</span>
                            </td>
                            <td>
                                <a href="products.php?edit=<?= $product['id'] ?>" class="btn btn-primary btn-sm">تعديل</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<style>
    @media (max-width: 768px) {
        .admin-content > div:last-child {
            grid-template-columns: 1fr !important;
        }
    }
</style>

<?php include 'includes/footer.php'; ?>

