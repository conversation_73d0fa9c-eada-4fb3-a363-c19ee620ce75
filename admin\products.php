<?php
$page_title = 'إدارة المنتجات';
include 'includes/header.php';

$success = '';
$error = '';

// Handle Add Product
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add') {
    $category_id = intval($_POST['category_id'] ?? 0);
    $title = trim($_POST['title'] ?? '');
    $slug = trim($_POST['slug'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $stock = intval($_POST['stock'] ?? 0);
    
    if (empty($title) || $price <= 0) {
        $error = 'الرجاء إدخال اسم المنتج والسعر';
    } else {
        if (empty($slug)) {
            $slug = generateSlug($title);
        }
        
        // Handle image upload
        $image = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $allowed = ['jpg', 'jpeg', 'png', 'gif'];
            $filename = $_FILES['image']['name'];
            $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            
            if (in_array($ext, $allowed)) {
                $new_filename = uniqid() . '.' . $ext;
                $upload_path = UPLOAD_DIR . $new_filename;
                
                if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    $image = $new_filename;
                }
            }
        }
        
        try {
            $stmt = $pdo->prepare("INSERT INTO products (category_id, title, slug, description, price, stock, image) 
                                   VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$category_id, $title, $slug, $description, $price, $stock, $image]);
            $success = 'تم إضافة المنتج بنجاح';
        } catch (PDOException $e) {
            $error = 'خطأ: المنتج موجود مسبقاً أو الرابط مكرر';
        }
    }
}

// Handle Edit Product
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'edit') {
    $id = intval($_POST['id'] ?? 0);
    $category_id = intval($_POST['category_id'] ?? 0);
    $title = trim($_POST['title'] ?? '');
    $slug = trim($_POST['slug'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $stock = intval($_POST['stock'] ?? 0);
    
    if ($id > 0 && !empty($title) && $price > 0) {
        if (empty($slug)) {
            $slug = generateSlug($title);
        }
        
        // Get current product
        $stmt = $pdo->prepare("SELECT image FROM products WHERE id = ?");
        $stmt->execute([$id]);
        $current_product = $stmt->fetch();
        $image = $current_product['image'];
        
        // Handle image upload
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $allowed = ['jpg', 'jpeg', 'png', 'gif'];
            $filename = $_FILES['image']['name'];
            $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            
            if (in_array($ext, $allowed)) {
                $new_filename = uniqid() . '.' . $ext;
                $upload_path = UPLOAD_DIR . $new_filename;
                
                if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    // Delete old image
                    if (!empty($image) && file_exists(UPLOAD_DIR . $image)) {
                        unlink(UPLOAD_DIR . $image);
                    }
                    $image = $new_filename;
                }
            }
        }
        
        try {
            $stmt = $pdo->prepare("UPDATE products SET category_id = ?, title = ?, slug = ?, description = ?, 
                                   price = ?, stock = ?, image = ? WHERE id = ?");
            $stmt->execute([$category_id, $title, $slug, $description, $price, $stock, $image, $id]);
            $success = 'تم تحديث المنتج بنجاح';
        } catch (PDOException $e) {
            $error = 'خطأ: الرابط مكرر';
        }
    }
}

// Handle Delete Product
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);
    
    // Get product image
    $stmt = $pdo->prepare("SELECT image FROM products WHERE id = ?");
    $stmt->execute([$id]);
    $product = $stmt->fetch();
    
    if ($product) {
        // Delete image
        if (!empty($product['image']) && file_exists(UPLOAD_DIR . $product['image'])) {
            unlink(UPLOAD_DIR . $product['image']);
        }
        
        // Delete product
        $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
        $stmt->execute([$id]);
        $success = 'تم حذف المنتج بنجاح';
    }
}

// Get all products
$stmt = $pdo->query("SELECT p.*, c.name as category_name 
                     FROM products p 
                     LEFT JOIN categories c ON p.category_id = c.id 
                     ORDER BY p.created_at DESC");
$products = $stmt->fetchAll();

// Get categories for dropdown
$stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
$categories = $stmt->fetchAll();

// Get product for editing
$edit_product = null;
if (isset($_GET['edit'])) {
    $id = intval($_GET['edit']);
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
    $stmt->execute([$id]);
    $edit_product = $stmt->fetch();
}
?>

<div class="admin-header">
    <h1>إدارة المنتجات</h1>
</div>

<?php if ($success): ?>
    <div class="alert alert-success"><?= $success ?></div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error"><?= $error ?></div>
<?php endif; ?>

<!-- Add/Edit Form -->
<div class="admin-card">
    <h2><?= $edit_product ? 'تعديل المنتج' : 'إضافة منتج جديد' ?></h2>
    <form method="post" enctype="multipart/form-data">
        <input type="hidden" name="action" value="<?= $edit_product ? 'edit' : 'add' ?>">
        <?php if ($edit_product): ?>
            <input type="hidden" name="id" value="<?= $edit_product['id'] ?>">
        <?php endif; ?>
        
        <div class="form-row">
            <div class="form-group">
                <label for="title">اسم المنتج *</label>
                <input type="text" 
                       name="title" 
                       id="title" 
                       class="form-control" 
                       value="<?= htmlspecialchars($edit_product['title'] ?? '') ?>"
                       required>
            </div>
            
            <div class="form-group">
                <label for="slug">الرابط (Slug)</label>
                <input type="text" 
                       name="slug" 
                       id="slug" 
                       class="form-control" 
                       value="<?= htmlspecialchars($edit_product['slug'] ?? '') ?>"
                       placeholder="يتم إنشاؤه تلقائياً">
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="category_id">القسم</label>
                <select name="category_id" id="category_id" class="form-control">
                    <option value="0">بدون قسم</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?= $category['id'] ?>" 
                                <?= ($edit_product && $edit_product['category_id'] == $category['id']) ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label for="price">السعر (IQD) *</label>
                <input type="number" 
                       name="price" 
                       id="price" 
                       class="form-control" 
                       value="<?= $edit_product['price'] ?? '' ?>"
                       step="0.01"
                       min="0"
                       required>
            </div>
            
            <div class="form-group">
                <label for="stock">المخزون *</label>
                <input type="number" 
                       name="stock" 
                       id="stock" 
                       class="form-control" 
                       value="<?= $edit_product['stock'] ?? 0 ?>"
                       min="0"
                       required>
            </div>
        </div>
        
        <div class="form-group">
            <label for="description">الوصف</label>
            <textarea name="description" 
                      id="description" 
                      class="form-control" 
                      rows="4"><?= htmlspecialchars($edit_product['description'] ?? '') ?></textarea>
        </div>
        
        <div class="form-group">
            <label for="image">الصورة</label>
            <input type="file" 
                   name="image" 
                   id="image" 
                   class="form-control"
                   accept="image/*">
            <?php if ($edit_product && $edit_product['image']): ?>
                <div style="margin-top: 0.5rem;">
                    <img src="<?= UPLOAD_URL . $edit_product['image'] ?>" 
                         alt="Current image" 
                         style="max-width: 150px; border-radius: 5px;">
                </div>
            <?php endif; ?>
        </div>
        
        <div class="btn-group">
            <button type="submit" class="btn btn-primary">
                <?= $edit_product ? 'تحديث' : 'إضافة' ?>
            </button>
            
            <?php if ($edit_product): ?>
                <a href="products.php" class="btn btn-secondary">إلغاء</a>
            <?php endif; ?>
        </div>
    </form>
</div>

<!-- Products List -->
<div class="admin-card">
    <h2>قائمة المنتجات</h2>
    <?php if (empty($products)): ?>
        <p style="color: var(--text-secondary);">لا توجد منتجات</p>
    <?php else: ?>
        <table class="admin-table">
            <thead>
                <tr>
                    <th>الصورة</th>
                    <th>الاسم</th>
                    <th>القسم</th>
                    <th>السعر</th>
                    <th>المخزون</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($products as $product): ?>
                    <tr>
                        <td>
                            <?php if ($product['image']): ?>
                                <img src="<?= UPLOAD_URL . $product['image'] ?>" 
                                     alt="<?= htmlspecialchars($product['title']) ?>"
                                     onerror="this.src='https://via.placeholder.com/50x50'">
                            <?php else: ?>
                                <img src="https://via.placeholder.com/50x50" alt="No image">
                            <?php endif; ?>
                        </td>
                        <td><?= htmlspecialchars($product['title']) ?></td>
                        <td><?= htmlspecialchars($product['category_name'] ?? 'بدون قسم') ?></td>
                        <td><?= formatPrice($product['price']) ?></td>
                        <td>
                            <?php if ($product['stock'] < 5): ?>
                                <span class="badge badge-danger"><?= $product['stock'] ?></span>
                            <?php else: ?>
                                <span class="badge badge-success"><?= $product['stock'] ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="?edit=<?= $product['id'] ?>" class="btn btn-primary btn-sm">تعديل</a>
                                <a href="?delete=<?= $product['id'] ?>" 
                                   class="btn btn-danger btn-sm"
                                   onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')">حذف</a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>

