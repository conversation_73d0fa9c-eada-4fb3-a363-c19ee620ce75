<?php
$page_title = 'إدارة الطلبات';
include 'includes/header.php';

$success = '';
$error = '';

// Handle status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_status') {
    $order_id = intval($_POST['order_id'] ?? 0);
    $status = $_POST['status'] ?? '';
    
    if ($order_id > 0 && in_array($status, ['pending', 'completed', 'cancelled'])) {
        $stmt = $pdo->prepare("UPDATE orders SET status = ? WHERE id = ?");
        $stmt->execute([$status, $order_id]);
        $success = 'تم تحديث حالة الطلب بنجاح';
    }
}

// Get order details if viewing
$view_order = null;
$order_items = [];
if (isset($_GET['view'])) {
    $order_id = intval($_GET['view']);
    
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
    $stmt->execute([$order_id]);
    $view_order = $stmt->fetch();
    
    if ($view_order) {
        $stmt = $pdo->prepare("SELECT oi.*, p.title, p.image 
                               FROM order_items oi 
                               LEFT JOIN products p ON oi.product_id = p.id 
                               WHERE oi.order_id = ?");
        $stmt->execute([$order_id]);
        $order_items = $stmt->fetchAll();
    }
}

// Get all orders
$stmt = $pdo->query("SELECT * FROM orders ORDER BY created_at DESC");
$orders = $stmt->fetchAll();
?>

<div class="admin-header">
    <h1>إدارة الطلبات</h1>
</div>

<?php if ($success): ?>
    <div class="alert alert-success"><?= $success ?></div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-error"><?= $error ?></div>
<?php endif; ?>

<?php if ($view_order): ?>
    <!-- Order Details -->
    <div class="admin-card">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
            <h2>تفاصيل الطلب #<?= $view_order['id'] ?></h2>
            <a href="orders.php" class="btn btn-secondary">← العودة للقائمة</a>
        </div>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
            <div>
                <h3 style="color: var(--dark-blue); margin-bottom: 1rem;">معلومات العميل</h3>
                <p><strong>الاسم:</strong> <?= htmlspecialchars($view_order['customer_name']) ?></p>
                <p><strong>الهاتف:</strong> <?= htmlspecialchars($view_order['phone']) ?></p>
                <p><strong>العنوان:</strong> <?= nl2br(htmlspecialchars($view_order['address'])) ?></p>
                <?php if (!empty($view_order['notes'])): ?>
                    <p><strong>ملاحظات:</strong> <?= nl2br(htmlspecialchars($view_order['notes'])) ?></p>
                <?php endif; ?>
            </div>
            
            <div>
                <h3 style="color: var(--dark-blue); margin-bottom: 1rem;">معلومات الطلب</h3>
                <p><strong>رقم الطلب:</strong> #<?= $view_order['id'] ?></p>
                <p><strong>التاريخ:</strong> <?= date('Y-m-d H:i', strtotime($view_order['created_at'])) ?></p>
                <p><strong>المجموع:</strong> <?= formatPrice($view_order['total']) ?></p>
                
                <form method="post" style="margin-top: 1rem;">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="order_id" value="<?= $view_order['id'] ?>">
                    <div class="form-group">
                        <label for="status"><strong>الحالة:</strong></label>
                        <select name="status" id="status" class="form-control" onchange="this.form.submit()">
                            <option value="pending" <?= $view_order['status'] === 'pending' ? 'selected' : '' ?>>قيد المعالجة</option>
                            <option value="completed" <?= $view_order['status'] === 'completed' ? 'selected' : '' ?>>مكتمل</option>
                            <option value="cancelled" <?= $view_order['status'] === 'cancelled' ? 'selected' : '' ?>>ملغي</option>
                        </select>
                    </div>
                </form>
            </div>
        </div>
        
        <h3 style="color: var(--dark-blue); margin-bottom: 1rem;">المنتجات</h3>
        <table class="admin-table">
            <thead>
                <tr>
                    <th>الصورة</th>
                    <th>المنتج</th>
                    <th>السعر</th>
                    <th>الكمية</th>
                    <th>المجموع</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($order_items as $item): ?>
                    <tr>
                        <td>
                            <?php if ($item['image']): ?>
                                <img src="<?= UPLOAD_URL . $item['image'] ?>" 
                                     alt="<?= htmlspecialchars($item['title']) ?>"
                                     onerror="this.src='https://via.placeholder.com/50x50'">
                            <?php else: ?>
                                <img src="https://via.placeholder.com/50x50" alt="No image">
                            <?php endif; ?>
                        </td>
                        <td><?= htmlspecialchars($item['title']) ?></td>
                        <td><?= formatPrice($item['price']) ?></td>
                        <td><?= $item['qty'] ?></td>
                        <td><strong><?= formatPrice($item['price'] * $item['qty']) ?></strong></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="4" style="text-align: left;"><strong>المجموع الكلي:</strong></td>
                    <td><strong style="color: var(--primary-blue); font-size: 1.2rem;"><?= formatPrice($view_order['total']) ?></strong></td>
                </tr>
            </tfoot>
        </table>
        
        <div style="margin-top: 2rem; text-align: center;">
            <a href="<?= SITE_URL ?>/invoice.php?order_id=<?= $view_order['id'] ?>" 
               target="_blank" 
               class="btn btn-primary">
                عرض الفاتورة
            </a>
        </div>
    </div>
<?php else: ?>
    <!-- Orders List -->
    <div class="admin-card">
        <h2>قائمة الطلبات</h2>
        <?php if (empty($orders)): ?>
            <p style="color: var(--text-secondary);">لا توجد طلبات</p>
        <?php else: ?>
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>العميل</th>
                        <th>الهاتف</th>
                        <th>المبلغ</th>
                        <th>الحالة</th>
                        <th>التاريخ</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($orders as $order): ?>
                        <tr>
                            <td><strong>#<?= $order['id'] ?></strong></td>
                            <td><?= htmlspecialchars($order['customer_name']) ?></td>
                            <td><?= htmlspecialchars($order['phone']) ?></td>
                            <td><?= formatPrice($order['total']) ?></td>
                            <td>
                                <?php if ($order['status'] === 'pending'): ?>
                                    <span class="badge badge-warning">قيد المعالجة</span>
                                <?php elseif ($order['status'] === 'completed'): ?>
                                    <span class="badge badge-success">مكتمل</span>
                                <?php elseif ($order['status'] === 'cancelled'): ?>
                                    <span class="badge badge-danger">ملغي</span>
                                <?php endif; ?>
                            </td>
                            <td><?= date('Y-m-d H:i', strtotime($order['created_at'])) ?></td>
                            <td>
                                <a href="?view=<?= $order['id'] ?>" class="btn btn-primary btn-sm">عرض</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
<?php endif; ?>

<style>
    @media (max-width: 768px) {
        .admin-card > div:first-child {
            grid-template-columns: 1fr !important;
        }
    }
</style>

<?php include 'includes/footer.php'; ?>

